# Nuxt dev/build outputs
.output
.data
.nuxt
.nitro
.cache
dist


# Node dependencies
node_modules

# Logs
logs
*.log

# Misc
.DS_Store
.fleet
.idea
.claude
.gemini
.opencode
https://login.microsoftonline.com/525e9c34-ba0f-4d67-82b6-acda016765d2/oauth2/v2.0/authorize?client_id=400f1e8f-880c-4aa3-b52b-7f451383c45c&response_type=code&redirect_uri=http://localhost:8005/api/query/callback&response_mode=query&scope=openid%20profile%20email%20api://400f1e8f-880c-4aa3-b52b-7f451383c45c/user_impersonation&state=12345
# Local env files
.env
.env.*
!.env.example

# VSC
.history
