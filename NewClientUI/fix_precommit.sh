#!/bin/bash

# Pre-commit Fix Script for NewClientUI
# This script fixes common issues that cause pre-commit hooks to fail
# Run this before committing to ensure your code passes all checks

echo "╔════════════════════════════════════════════════════════╗"
echo "║     Pre-commit Fix Script for NewClientUI             ║"
echo "╚════════════════════════════════════════════════════════╝"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Starting pre-commit fixes...${NC}"
echo ""

# Counter for fixed files
FIXED_COUNT=0

# Fix trailing whitespace in all relevant files
echo -e "${YELLOW}→ Removing trailing whitespace...${NC}"
WHITESPACE_FILES=$(find app -type f \( -name "*.vue" -o -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.css" -o -name "*.scss" -o -name "*.json" -o -name "*.yaml" -o -name "*.yml" -o -name "*.md" \) -exec grep -l '[[:space:]]$' {} \; 2>/dev/null | wc -l | tr -d ' ')
find app -type f \( -name "*.vue" -o -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.css" -o -name "*.scss" -o -name "*.json" -o -name "*.yaml" -o -name "*.yml" -o -name "*.md" \) -exec sed -i '' 's/[[:space:]]*$//' {} \; 2>/dev/null
echo "  ✓ Fixed trailing whitespace in $WHITESPACE_FILES files"

# Fix trailing whitespace in root config files (including hidden files)
ROOT_FILES=0
for file in *.ts *.js *.json *.md *.yml *.yaml README.md package.json tsconfig.json .gitignore .eslintrc* .prettierrc* .env* .npmrc; do
    if [ -f "$file" ]; then
        if grep -q '[[:space:]]$' "$file" 2>/dev/null; then
            sed -i '' 's/[[:space:]]*$//' "$file" 2>/dev/null
            ((ROOT_FILES++))
        fi
    fi
done
echo "  ✓ Fixed trailing whitespace in $ROOT_FILES root files"

# Ensure all files end with a newline
echo -e "${YELLOW}→ Ensuring files end with newline...${NC}"
NEWLINE_COUNT=0
find app -type f \( -name "*.vue" -o -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.css" -o -name "*.scss" -o -name "*.json" \) | while read -r file; do
    if [ -n "$(tail -c 1 "$file" 2>/dev/null)" ]; then
        echo "" >> "$file"
        ((NEWLINE_COUNT++))
    fi
done

# Fix root config files to end with newline (including hidden files)
for file in *.ts *.js *.json nuxt.config.ts tailwind.config.ts tsconfig.json package.json README.md .gitignore .eslintrc* .prettierrc* .env* .npmrc .dockerignore Dockerfile; do
    if [ -f "$file" ]; then
        if [ -n "$(tail -c 1 "$file" 2>/dev/null)" ]; then
            echo "" >> "$file"
            ((NEWLINE_COUNT++))
        fi
    fi
done
echo "  ✓ Added missing newlines to files"

# Remove multiple consecutive blank lines (clean up after comment removal)
echo -e "${YELLOW}→ Removing excessive blank lines...${NC}"
find app -type f \( -name "*.vue" -o -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) -exec sed -i '' '/^$/N;/^\n$/d' {} \; 2>/dev/null
echo "  ✓ Cleaned up excessive blank lines"

# Fix specific files that commonly fail (including .gitignore)
echo -e "${YELLOW}→ Checking specific problem files...${NC}"
PROBLEM_FILES=("app/middleware/auth.global.ts" "app/pages/index.vue" "app/components/UserMenu.vue" ".gitignore" ".eslintrc" ".prettierrc")
for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$file" ]; then
        sed -i '' 's/[[:space:]]*$//' "$file" 2>/dev/null
        if [ -n "$(tail -c 1 "$file" 2>/dev/null)" ]; then
            echo "" >> "$file"
        fi
        echo "  ✓ Fixed: $file"
    fi
done

echo ""
echo -e "${GREEN}╔════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║     ✅ Pre-commit fixes completed successfully!       ║${NC}"
echo -e "${GREEN}╚════════════════════════════════════════════════════════╝${NC}"
echo ""
echo "📋 Summary of fixes applied:"
echo "  • Removed trailing whitespace from all files"
echo "  • Ensured all files end with a newline"
echo "  • Removed excessive blank lines"
echo "  • Fixed known problem files"
echo ""
echo "📁 File types processed:"
echo "  • Vue components (.vue)"
echo "  • TypeScript files (.ts, .tsx)"
echo "  • JavaScript files (.js, .jsx)"
echo "  • Style files (.css, .scss)"
echo "  • Config files (.json, .yaml, .yml)"
echo "  • Documentation (.md)"
echo ""
echo -e "${BLUE}💡 Tip: Run this script before committing to avoid CI/CD failures${NC}"
echo -e "${BLUE}   Usage: ./fix_precommit.sh${NC}"
echo ""
