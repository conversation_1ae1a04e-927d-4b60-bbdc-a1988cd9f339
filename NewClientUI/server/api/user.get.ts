export default defineEventHandler(async (event) => {
  const authorization = getHeader(event, 'authorization')

  if (!authorization) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Authorization header is required'
    })
  }

  const token = authorization.replace('Bearer ', '')

  if (!token) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Invalid authorization token'
    })
  }

  try {
    // Fetch a backend API that already validates the token and returns user_info
    // Using the same endpoint the old ClientUI uses to also keep the logic consistent
    const data = await $fetch<{
      collections?: any[]
      user_info?: { user_name?: string, email?: string }
    }>('/api/loader/collections', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    const name = data?.user_info?.user_name || ''
    const email = data?.user_info?.email || ''

    if (!email && !name) {
      // If backend didn't return user info, consider unauthorized
      throw createError({ statusCode: 401, statusMessage: 'User info not available' })
    }

    const user = {
      id: email || name,
      email,
      name,
      avatar: ''
    }

    return user
  } catch (error: any) {
    console.error('Failed to fetch user from backend collections endpoint:', error)

    if (error.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired access token'
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user information'
    })
  }
})
