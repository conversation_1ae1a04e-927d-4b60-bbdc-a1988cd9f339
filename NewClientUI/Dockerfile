# Use Node.js runtime for development server
FROM node:20-alpine

WORKDIR /app

# Install bash for environment variable substitution
RUN apk add --no-cache bash

# Copy package files
COPY NewClientUI/package*.json ./

# Install dependencies with npm
RUN npm install

# Copy source code
COPY NewClientUI/ ./

# Copy environment substitution script
COPY NewClientUI/replacer /replacer
RUN chmod +x /replacer

# Expose port
EXPOSE 8005

# Start the development server with environment variable substitution
CMD ["/bin/bash", "-c", "/replacer && npm run dev -- --host 0.0.0.0 --port 8005"]
