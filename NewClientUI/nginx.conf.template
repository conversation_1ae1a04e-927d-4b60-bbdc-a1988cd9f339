user                            root;
worker_processes                auto;

error_log                       /var/log/nginx/error.log warn;

events {
    worker_connections          1024;
}

http {
    include                     /etc/nginx/mime.types;
    default_type                application/octet-stream;
    sendfile                    off;
    access_log                  off;
    keepalive_timeout           3000;

    server {
        listen                  8005;
        root                    /usr/share/nginx/html;
        index                   index.html;
        server_name             localhost;
        client_max_body_size    50m;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /api/query/ {
            proxy_pass ${QUERY_BASE_URL}/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Important for streaming responses
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;

            # Ensure headers are passed through
            proxy_pass_header X-Sessions-Id;

            # For SSE/streaming
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            chunked_transfer_encoding off;
        }

        location /slash-commands/ {
            proxy_pass ${QUERY_BASE_URL}/slash-commands/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /api/loader/ {
            proxy_pass ${LOADER_BASE_URL}/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

    }
}
