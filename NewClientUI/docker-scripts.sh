#!/bin/bash

# Docker utility scripts for NewClientUI
# This script provides easy commands to build and run development and production containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="newclientui"
DEV_PORT=8005
PROD_PORT=3000
NETWORK="embeddingservice_frontend"

show_help() {
    echo "╔════════════════════════════════════════════════════════════════╗"
    echo "║              Docker Scripts for NewClientUI                    ║"
    echo "╚════════════════════════════════════════════════════════════════╝"
    echo ""
    echo "Usage: ./docker-scripts.sh [command]"
    echo ""
    echo "Commands:"
    echo "  dev-build    - Build development Docker image"
    echo "  dev-run      - Run development container with hot reload"
    echo "  dev          - Build and run development container"
    echo ""
    echo "  prod-build   - Build production Docker image"
    echo "  prod-run     - Run production container"
    echo "  prod         - Build and run production container"
    echo ""
    echo "  stop         - Stop all NewClientUI containers"
    echo "  clean        - Remove all NewClientUI containers and images"
    echo "  logs         - Show container logs"
    echo ""
    echo "Examples:"
    echo "  ./docker-scripts.sh dev          # Start development environment"
    echo "  ./docker-scripts.sh prod         # Deploy production build"
    echo "  ./docker-scripts.sh logs         # View logs"
    echo ""
}

dev_build() {
    echo -e "${BLUE}🔨 Building development Docker image...${NC}"
    cd ..
    docker build -f NewClientUI/Dockerfile.dev -t ${PROJECT_NAME}:dev .
    echo -e "${GREEN}✅ Development image built successfully${NC}"
}

dev_run() {
    echo -e "${BLUE}🚀 Starting development container...${NC}"
    cd ..
    
    # Stop any existing container
    docker stop ${PROJECT_NAME}-dev 2>/dev/null || true
    docker rm ${PROJECT_NAME}-dev 2>/dev/null || true
    
    # Run development container with volume mounts for hot reload
    docker run -d \
        --name ${PROJECT_NAME}-dev \
        -p ${DEV_PORT}:${DEV_PORT} \
        -v $(pwd)/NewClientUI:/app \
        -v /app/node_modules \
        -v /app/.nuxt \
        --network ${NETWORK} \
        --env-file NewClientUI/.env \
        -e NODE_ENV=development \
        ${PROJECT_NAME}:dev
    
    echo -e "${GREEN}✅ Development container started${NC}"
    echo -e "${YELLOW}📍 Access the application at: http://localhost:${DEV_PORT}${NC}"
    echo -e "${YELLOW}📝 Logs: docker logs -f ${PROJECT_NAME}-dev${NC}"
}

prod_build() {
    echo -e "${BLUE}🔨 Building production Docker image...${NC}"
    cd ..
    docker build -f NewClientUI/Dockerfile -t ${PROJECT_NAME}:prod .
    echo -e "${GREEN}✅ Production image built successfully${NC}"
}

prod_run() {
    echo -e "${BLUE}🚀 Starting production container...${NC}"
    cd ..
    
    # Stop any existing container
    docker stop ${PROJECT_NAME}-prod 2>/dev/null || true
    docker rm ${PROJECT_NAME}-prod 2>/dev/null || true
    
    # Run production container
    docker run -d \
        --name ${PROJECT_NAME}-prod \
        -p ${PROD_PORT}:${PROD_PORT} \
        --network ${NETWORK} \
        --env-file NewClientUI/.env \
        -e NODE_ENV=production \
        --restart unless-stopped \
        ${PROJECT_NAME}:prod
    
    echo -e "${GREEN}✅ Production container started${NC}"
    echo -e "${YELLOW}📍 Access the application at: http://localhost:${PROD_PORT}${NC}"
    echo -e "${YELLOW}📝 Logs: docker logs -f ${PROJECT_NAME}-prod${NC}"
}

stop_containers() {
    echo -e "${YELLOW}⏹️  Stopping NewClientUI containers...${NC}"
    docker stop ${PROJECT_NAME}-dev 2>/dev/null || true
    docker stop ${PROJECT_NAME}-prod 2>/dev/null || true
    echo -e "${GREEN}✅ Containers stopped${NC}"
}

clean_all() {
    echo -e "${RED}🧹 Cleaning up NewClientUI containers and images...${NC}"
    
    # Stop and remove containers
    docker stop ${PROJECT_NAME}-dev 2>/dev/null || true
    docker stop ${PROJECT_NAME}-prod 2>/dev/null || true
    docker rm ${PROJECT_NAME}-dev 2>/dev/null || true
    docker rm ${PROJECT_NAME}-prod 2>/dev/null || true
    
    # Remove images
    docker rmi ${PROJECT_NAME}:dev 2>/dev/null || true
    docker rmi ${PROJECT_NAME}:prod 2>/dev/null || true
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

show_logs() {
    # Check which container is running
    if docker ps | grep -q ${PROJECT_NAME}-dev; then
        docker logs -f ${PROJECT_NAME}-dev
    elif docker ps | grep -q ${PROJECT_NAME}-prod; then
        docker logs -f ${PROJECT_NAME}-prod
    else
        echo -e "${RED}❌ No NewClientUI container is running${NC}"
        echo "Available containers:"
        docker ps -a | grep ${PROJECT_NAME} || echo "No containers found"
    fi
}

# Main script logic
case "$1" in
    dev-build)
        dev_build
        ;;
    dev-run)
        dev_run
        ;;
    dev)
        dev_build
        dev_run
        ;;
    prod-build)
        prod_build
        ;;
    prod-run)
        prod_run
        ;;
    prod)
        prod_build
        prod_run
        ;;
    stop)
        stop_containers
        ;;
    clean)
        clean_all
        ;;
    logs)
        show_logs
        ;;
    *)
        show_help
        ;;
esac
