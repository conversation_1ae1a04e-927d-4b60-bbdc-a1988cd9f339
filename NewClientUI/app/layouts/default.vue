<script setup lang="ts">
import ModalConfirm from '~/components/ModalConfirm.vue'

const route = useRoute()
const toast = useToast()
const overlay = useOverlay()
const { isAuthenticated } = useAuth()
const loggedIn = computed(() => isAuthenticated.value)
const openInPopup = () => {}

const open = ref(false)

const deleteModal = overlay.create(ModalConfirm, {
  props: {
    title: 'Delete chat',
    description: 'Are you sure you want to delete this chat? This cannot be undone.'
  }
})

const { getChats, getChatById } = useDummyData()
const { data: chats, refresh: refreshChats } = await useLazyAsyncData('chats', getChats, {
  transform: data => data.map(chat => ({
    id: chat.id,
    label: chat.label || 'Untitled',
    to: `/chat/${chat.id}`,
    icon: chat.icon,
    createdAt: chat.createdAt
  }))
})

onNuxtReady(async () => {
  const first10 = (chats.value || []).slice(0, 10)
  for (const chat of first10) {
    // prefetch the chat and let the browser cache it
    await mockGetChatById(chat.id)
  }
})

const { groups } = useChats(chats)

const items = computed(() => groups.value?.flatMap((group) => {
  return [{
    label: group.label,
    type: 'label' as const
  }, ...group.items.map(item => ({
    ...item,
    slot: 'chat' as const,
    icon: undefined,
    class: item.label === 'Untitled' ? 'text-muted' : ''
  }))]
}))

async function handleDeleteChat(id: string) {
  const instance = deleteModal.open()
  const result = await instance.result
  if (!result) {
    return
  }

  const { deleteChat } = useDummyData()
  await deleteChat(id)

  toast.add({
    title: 'Chat deleted',
    description: 'Your chat has been deleted',
    icon: 'i-lucide-trash'
  })

  await refreshChats()

  if (route.params.id === id) {
    navigateTo('/')
  }
}

defineShortcuts({
  c: () => {
    navigateTo('/')
  }
})
</script>

<template>
  <UiMeshBackground :show-mesh-box="true" mesh-box-position="left" :mesh-box-opacity="1">
    <UDashboardGroup unit="rem">
      <UDashboardSidebar
        id="default"
        v-model:open="open"
        :min-size="12"
        collapsible
        resizable
        class="bg-elevated/50"
      >
        <template #header="{ collapsed }">
          <NuxtLink to="/landing" class="flex items-end gap-0.5">
            <Logo class="h-8 w-auto shrink-0" />
            <span v-if="!collapsed" class="text-xl font-bold text-highlighted">_fbeta</span>
          </NuxtLink>

          <div v-if="!collapsed" class="flex items-center gap-1.5 ms-auto">
            <UDashboardSearchButton collapsed />
            <UDashboardSidebarCollapse />
          </div>
        </template>

        <template #default="{ collapsed }">
          <div class="flex flex-col gap-1.5">
            <UButton
              v-bind="collapsed ? { icon: 'i-lucide-plus' } : { label: 'New chat' }"
              variant="soft"
              block
              to="/chat"
              @click="open = false"
            />

            <template v-if="collapsed">
              <UDashboardSearchButton collapsed />
              <UDashboardSidebarCollapse />
            </template>
          </div>

          <UNavigationMenu
            v-if="!collapsed"
            :items="items"
            :collapsed="collapsed"
            orientation="vertical"
            :ui="{ link: 'overflow-hidden' }"
          >
            <template #chat-trailing="{ item }">
              <div class="flex -mr-1.25 translate-x-full group-hover:translate-x-0 transition-transform">
                <UButton
                  icon="i-lucide-x"
                  color="neutral"
                  variant="ghost"
                  size="xs"
                  class="text-muted hover:text-primary hover:bg-accented/50 focus-visible:bg-accented/50 p-0.5"
                  tabindex="-1"
                  @click.stop.prevent="handleDeleteChat((item as any).id)"
                />
              </div>
            </template>
          </UNavigationMenu>
        </template>

        <template #footer="{ collapsed }">
          <UButton
            v-if="!loggedIn"
            :label="collapsed ? '' : 'Login with GitHub'"
            icon="i-simple-icons-github"
            color="neutral"
            variant="ghost"
            class="w-full"
            @click="openInPopup()"
          />
        </template>
      </UDashboardSidebar>

      <UDashboardSearch
        placeholder="Search chats..."
        :groups="[{
          id: 'links',
          items: [{
            label: 'New chat',
            to: '/',
            icon: 'i-lucide-square-pen'
          }]
        }, ...groups]"
      />

      <slot />
    </UDashboardGroup>

    <!-- User Menu positioned in top right corner -->
    <div v-if="loggedIn" class="fixed top-4 right-4 z-50">
      <UserMenu />
    </div>
  </UiMeshBackground>
</template>
