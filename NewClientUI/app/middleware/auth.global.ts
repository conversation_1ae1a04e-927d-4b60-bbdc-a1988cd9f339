/**
 * Global Auth Middleware
 * Handles authentication checks for protected routes
 */

export default defineNuxtRouteMiddleware(async (to) => {
  // Skip middleware on server-side rendering
  if (import.meta.server) return

  const { isAuthenticated, isLoading, initialize, getToken } = useAuth()

  // Define public routes that don't require authentication
  const publicRoutes = ['/login', '/']
  const isPublicRoute = publicRoutes.includes(to.path)

  // Check if route explicitly requires auth (default: true for most routes)
  const requiresAuth = to.meta.auth !== false

  // If there's a token but user is not authenticated, initialize auth
  if (getToken() && !isAuthenticated.value && !isLoading.value) {
    // Trigger initialization but don't block the navigation cycle.
    // This prevents premature redirects during page refresh.
    initialize()
  }

  // If route requires auth and user is not authenticated
  if (requiresAuth && !isAuthenticated.value && !isPublicRoute) {
    // Don't redirect if still loading
    if (isLoading.value) {
      return
    }

    // If there's no token, redirect to login
    if (!getToken()) {
      return navigateTo('/')
    }
  }

  // If user is authenticated and trying to access the login page or index
  if (isAuthenticated.value && (to.path === '/' || to.path === '/login')) {
    return navigateTo('/landing')
  }

  // If user has token (potentially authenticated) and trying to access index, redirect to landing
  // This prevents the flash between / and /landing
  if (getToken() && to.path === '/' && !isLoading.value) {
    return navigateTo('/landing')
  }
})
