import type { Store } from 'pinia'
import type { AuthStoreState } from '~/stores/auth'
import type { User } from '~/composables/useAuth'

declare module 'pinia' {
  export interface PiniaCustomProperties {
    // Auth store actions
    setUser(user: User | null): void
    setToken(token: string | null): void
    setLoading(loading: boolean): void
    setError(error: string | null): void
    updateUserProfile(updates: Partial<User>): void
    logout(): void
    initialize(token: string | null, user: User | null): void
  }
}

// Augment the auth store type
declare module '~/stores/auth' {
  interface AuthStore extends Store<'auth', AuthStoreState> {
    setUser(user: User | null): void
    setToken(token: string | null): void
    setLoading(loading: boolean): void
    setError(error: string | null): void
    updateUserProfile(updates: Partial<User>): void
    logout(): void
    initialize(token: string | null, user: User | null): void
  }
}
