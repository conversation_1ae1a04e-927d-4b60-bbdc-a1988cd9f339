/**
 * API Configuration and Endpoints
 * Centralized configuration for all API endpoints and settings
 */

// Base URLs from environment variables
export const API_BASE_URLS = {
  LOADER: process.env.LOADER_BASE_URL || 'http://localhost:8002',
  INDEXER: process.env.INDEXER_BASE_URL || 'http://localhost:8001',
  QUERY: process.env.QUERY_BASE_URL || 'http://localhost:8003'
} as const

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN_URL: '/login-url',
    LOGOUT: '/logout',
    USER: '/api/user'
  },

  // Sessions
  SESSIONS: {
    LIST: '/api/sessions',
    CREATE: '/api/sessions',
    GET: (id: string) => `/api/sessions/${id}`,
    DELETE: (id: string) => `/api/sessions/${id}`,
    RENAME: (id: string) => `/api/sessions/${id}/rename`,
    CREATE_FOLDER: (collectionId: string, folderName: string) =>
      `/api/query/create_folder/${collectionId}/${encodeURIComponent(folderName)}`
  },

  // Chat
  CHAT: {
    QUERY: '/api/query/',
    STREAM: '/api/query/stream'
  },

  // Collections
  COLLECTIONS: {
    LIST: '/api/collections',
    GET: (id: string) => `/api/collections/${id}`
  },

  // File Upload
  UPLOAD: {
    FILES: '/api/loader/upload/'
  },

  // Prompts
  PROMPTS: {
    SEARCH: '/api/prompts'
  },

  // Slash Commands
  SLASH_COMMANDS: '/slash-commands/'
} as const

// Request Configuration
export const REQUEST_CONFIG = {
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000 // 1 second
} as const

// Auth Configuration
export const AUTH_CONFIG = {
  TOKEN_KEY: 'access_token',
  TOKEN_HEADER: 'Authorization',
  TOKEN_PREFIX: 'Bearer'
} as const

// Client Configuration
export const CLIENT_CONFIG = {
  FE_STYLE: process.env.FE_STYLE || 'fbeta',
  SUPPORTED_CLIENTS: ['fbeta', 'audi', 'aok', 'cevey', 'dak', 'windmoller'] as const
} as const

export type SupportedClient = typeof CLIENT_CONFIG.SUPPORTED_CLIENTS[number]
