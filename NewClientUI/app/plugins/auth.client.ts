export default defineNuxtPlugin(async () => {
  const { initialize, handleTokenFromUrl } = useAuth()
  const authStore = useAuthStore()

  authStore.setLoading(true)

  try {
    // Handle OAuth callback token from URL
    const hasToken = handleTokenFromUrl()

    // Initialize auth state
    await initialize()

    // Small delay to prevent flash
    if (hasToken) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  } finally {
    authStore.setLoading(false)
  }
})
