import { ref, computed, watchEffect } from 'vue'
import { useAppStore } from '~/stores/app'
import { useCollectionsStore } from '~/stores/collections'
import { useDialog } from '~/composables/useDialog'
import { useToast } from '#imports'

export const useSidebar = () => {
  const appStore = useAppStore()
  const collectionsStore = useCollectionsStore()
  const { prompt } = useDialog()
  const toast = useToast()

  // State
  const searchQuery = ref('')
  const showSearch = ref(false)
  const expandedSections = ref<Record<string, boolean>>({
    'favorites': true,
    'unassigned': true,
    'demo-folder': true,
    'favoritesOnly': true
  })

  // Computed properties from store
  const favoriteSessions = computed(() => collectionsStore.favoriteSessions)
  const unassignedSessions = computed(() => collectionsStore.unassignedSessions)
  const folderSessions = computed(() => collectionsStore.folderSessions)
  const currentSessionId = computed(() => collectionsStore.currentSession?.id)
  const isLoadingSessions = computed(() => collectionsStore.isLoadingSessions)

  // Check if there are any real sessions
  const hasAnySessions = computed(() => {
    return favoriteSessions.value.length > 0
      || unassignedSessions.value.length > 0
      || Object.keys(folderSessions.value).length > 0
  })

  // Filtered sessions based on search
  const filteredFavorites = computed(() => {
    if (!searchQuery.value) return favoriteSessions.value
    return favoriteSessions.value.filter(s =>
      s.title?.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  })

  // Check if all sections are expanded
  const allSectionsExpanded = computed(() => {
    if (appStore.showFavorites) {
      return expandedSections.value.favoritesOnly
    } else {
      const relevantSections = Object.entries(expandedSections.value)
        .filter(([key]) => key !== 'favoritesOnly')
      return relevantSections.every(([_, value]) => value)
    }
  })

  // Initialize expanded sections for folders
  watchEffect(() => {
    Object.keys(folderSessions.value).forEach((folder) => {
      if (!(folder in expandedSections.value)) {
        expandedSections.value[folder] = false
      }
    })
  })

  // Methods
  const truncateName = (name: string) => {
    if (!name) return name
    if (name.length <= 15) return name
    return name.substring(0, 15) + '...'
  }

  const toggleSection = (section: string) => {
    expandedSections.value[section] = !expandedSections.value[section]
  }

  const toggleAllSections = () => {
    const targetState = !allSectionsExpanded.value

    if (appStore.showFavorites) {
      expandedSections.value.favoritesOnly = targetState
    } else {
      Object.keys(expandedSections.value).forEach((key) => {
        if (key !== 'favoritesOnly') {
          expandedSections.value[key] = targetState
        }
      })
    }
  }

  const selectSession = async (sessionId: string) => {
    await collectionsStore.selectSession(sessionId)
    // Close sidebar on mobile after selection
    if (window.innerWidth < 768) {
      appStore.toggleSidebar()
    }
  }

  const handleNewChat = async () => {
    try {
      const session = await collectionsStore.createSession('', undefined)

      if (session && session.id) {
        await refreshSessions()
        await selectSession(session.id)

        // Emit event to notify chat page
        const event = new CustomEvent('session-created', { detail: { sessionId: session.id } })
        window.dispatchEvent(event)
      }
    } catch (error) {
      console.error('Error creating new session:', error)
    }
  }

  const handleCreateFolder = async () => {
    const folderName = await prompt({
      title: 'Neuen Ordner erstellen',
      placeholder: 'Ordnernamen eingeben',
      confirmText: 'Erstellen',
      cancelText: 'Abbrechen',
      required: true
    })

    if (folderName && folderName.trim()) {
      try {
        await collectionsStore.createFolder(folderName.trim())
        await refreshSessions()

        // Show success toast
        toast.add({
          title: 'Erfolg',
          description: `Ordner "${folderName.trim()}" wurde erfolgreich erstellt.`,
          color: 'success',
          icon: 'i-lucide-check-circle'
        })
      } catch (error: any) {
        console.error('Error creating folder:', error)

        // Show error toast
        let errorMessage = 'Ordner konnte nicht erstellt werden.'
        if (error.response?.data?.detail) {
          errorMessage = error.response.data.detail
        } else if (error.message) {
          errorMessage = error.message
        }

        toast.add({
          title: 'Fehler',
          description: errorMessage,
          color: 'primary',
          icon: 'i-lucide-alert-circle'
        })
      }
    }
  }

  const refreshSessions = async () => {
    if (collectionsStore.currentCollection) {
      await collectionsStore.fetchSessions(collectionsStore.currentCollection.id)
    }
  }

  const toggleSearch = () => {
    showSearch.value = !showSearch.value
    if (!showSearch.value) {
      searchQuery.value = ''
    }
  }

  const closeSearch = () => {
    showSearch.value = false
    searchQuery.value = ''
  }

  return {
    // State
    searchQuery,
    showSearch,
    expandedSections,

    // Computed
    favoriteSessions,
    unassignedSessions,
    folderSessions,
    currentSessionId,
    isLoadingSessions,
    hasAnySessions,
    filteredFavorites,
    allSectionsExpanded,

    // Methods
    truncateName,
    toggleSection,
    toggleAllSections,
    selectSession,
    handleNewChat,
    handleCreateFolder,
    refreshSessions,
    toggleSearch,
    closeSearch
  }
}
