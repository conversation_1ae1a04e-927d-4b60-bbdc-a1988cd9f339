/**
 * Authentication Composable
 * Manages user authentication, token storage, and auth state
 */

import { useLocalStorage } from '@vueuse/core'
import { AUTH_CONFIG, API_ENDPOINTS, API_BASE_URLS } from '~/config/api'
import { useAuthStore } from '~/stores/auth'

export interface User {
  id: string
  email: string
  name?: string
  avatar?: string
  [key: string]: any
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

/**
 * Authentication composable
 */
export function useAuth() {
  // Use Pinia store for state management
  const authStore = useAuthStore()

  // Token storage using VueUse localStorage
  const token = useLocalStorage<string | null>(AUTH_CONFIG.TOKEN_KEY, null)

  // Computed properties from store
  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const user = computed(() => authStore.user)
  const isLoading = computed(() => authStore.isLoading)
  const error = computed(() => authStore.error)

  /**
   * Initialize auth state on app start
   */
  async function initialize() {
    authStore.initialize(token.value, null)
    // Don't auto-fetch user - let components decide when to fetch
    // This prevents unnecessary API calls on every page load
    // if (import.meta.client && token.value) {
    //   await fetchUser()
    // }
  }

  function setToken(newToken: string) {
    token.value = newToken
    authStore.setToken(newToken)
    authStore.setError(null)
    console.log('Token set in localStorage and store:', newToken.substring(0, 20) + '...')
  }

  /**
   * Clear authentication token
   */
  function clearToken() {
    token.value = null
    authStore.logout()
  }

  /**
   * Handle token from URL (OAuth callback)
   */
  function handleTokenFromUrl() {
    if (import.meta.client) {
      const urlParams = new URLSearchParams(window.location.search)
      const accessToken = urlParams.get('access_token')
      const error = urlParams.get('error')

      if (error) {
        authStore.setError(error)
        window.history.replaceState({}, document.title, window.location.pathname)
        return false
      }

      if (accessToken) {
        setToken(accessToken)
        window.history.replaceState({}, document.title, window.location.pathname)
        return true
      }
    }
    return false
  }

  /**
   * Fetch user information
   */
  async function fetchUser(): Promise<User | null> {
    if (!token.value) {
      authStore.setUser(null)
      return null
    }

    authStore.setLoading(true)
    authStore.setError(null)

    try {
      console.log('Fetching user data from:', API_ENDPOINTS.AUTH.USER)
      const userData = await $fetch<User>(API_ENDPOINTS.AUTH.USER, {
        headers: {
          [AUTH_CONFIG.TOKEN_HEADER]: `${AUTH_CONFIG.TOKEN_PREFIX} ${token.value}`
        }
      })

      console.log('User data received:', userData)
      authStore.setUser(userData)
      return userData
    } catch (error: unknown) {
      console.error('Failed to fetch user:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user'

      // If unauthorized, clear token
      if ((error as any)?.status === 401) {
        clearToken()
      }

      authStore.setError(errorMessage)
      return null
    } finally {
      authStore.setLoading(false)
    }
  }

  /**
   * Login user (redirect to OAuth provider)
   */
  async function login(): Promise<void> {
    authStore.setLoading(true)
    authStore.setError(null)

    try {
      const response = await $fetch<{ login_url: string }>('/api/query/login-url', {

      })
      console.log('Login:', response)
      console.log('Login URL:', response.login_url)

      if (response.login_url) {
        await redirectToLogin(response.login_url)
      } else {
        throw new Error('Login URL not found')
      }
    } catch (error: unknown) {
      console.error('Login failed:', error)
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      authStore.setError(errorMessage)
    } finally {
      authStore.setLoading(false)
    }
  }

  /**
   * Redirect to login URL
   */
  async function redirectToLogin(loginUrl: string): Promise<void> {
    if (import.meta.client) {
      // Keep loading state active during redirect
      authStore.setLoading(true)
      window.location.href = loginUrl
    }
  }

  /**
   * Logout user
   */
  async function logout(): Promise<void> {
    authStore.setLoading(true)

    try {
      // Clear all local state first
      clearToken()

      // Clear any stored user data
      if (import.meta.client) {
        localStorage.removeItem('user_email')
        sessionStorage.removeItem('selectedCollection')
      }

      // The backend should handle the actual OAuth logout
      if (import.meta.client) {
        // Redirect to backend logout endpoint which will clear cookies/session
        window.location.href = '/'
      }
    } finally {
      authStore.setLoading(false)
    }
  }

  /**
   * Check if user has specific permission/role
   */
  function hasPermission(permission: string): boolean {
    return authStore.user?.permissions?.includes(permission) ?? false
  }

  /**
   * Check if user has specific role
   */
  function hasRole(role: string): boolean {
    return authStore.user?.roles?.includes(role) ?? false
  }

  /**
   * Refresh user data
   */
  async function refresh(): Promise<User | null> {
    return await fetchUser()
  }

  /**
   * Update user data
   */
  function updateUser(userData: Partial<User>) {
    authStore.updateUserProfile(userData)
  }

  const setIsLoading = (loading: boolean) => {
    authStore.setLoading(loading)
  }

  const setError = (error: string | null) => {
    authStore.setError(error)
  }

  const setUser = (user: User | null) => {
    authStore.setUser(user)
  }

  const updateUserProfile = (updates: Partial<User>) => {
    authStore.updateUserProfile(updates)
  }

  // Auto-initialize on client side
  if (import.meta.client) {
    // Handle token from URL first
    handleTokenFromUrl()

    // Initialize auth state without fetching user
    // User data will be fetched from collections API when needed
    initialize().catch(console.error)
  }

  /**
   * Get current token
   */
  function getToken(): string | null {
    return token.value
  }

  return {
    user: readonly(user),
    isAuthenticated: readonly(isAuthenticated),
    isLoading: readonly(isLoading),
    error: readonly(error),
    getToken,
    setToken,
    clearToken,
    handleTokenFromUrl,
    login,
    logout,
    refresh,
    fetchUser,
    redirectToLogin,
    updateUser,
    hasPermission,
    hasRole,
    initialize,
    setUser: authStore.setUser
  }
}
