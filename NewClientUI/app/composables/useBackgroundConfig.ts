export interface BackgroundConfig {
  variant: 'default' | 'dark' | 'light'
  animated: boolean
  showMeshBox: boolean
  meshBoxPosition: 'left' | 'right' | 'center'
  meshBoxOpacity: number
}

export function useBackgroundConfig() {
  const defaultConfig: BackgroundConfig = {
    variant: 'default',
    animated: false,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.4
  }

  const landingConfig: BackgroundConfig = {
    variant: 'default',
    animated: true,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.4
  }

  const authConfig: BackgroundConfig = {
    variant: 'default',
    animated: true,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.4
  }

  // Configuration for chat and main app areas
  const chatConfig: BackgroundConfig = {
    variant: 'default',
    animated: false,
    showMeshBox: true,
    meshBoxPosition: 'left',
    meshBoxOpacity: 0.3
  }

  return {
    defaultConfig,
    landingConfig,
    authConfig,
    chatConfig
  }
}
