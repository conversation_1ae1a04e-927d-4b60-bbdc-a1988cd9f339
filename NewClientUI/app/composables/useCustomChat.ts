import type { Ref } from 'vue'

// Browser-compatible UUID generator
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  createdAt?: string
}

export interface ChatOptions {
  id: string
  initialMessages?: ChatMessage[]
  onResponse?: (response: any) => void
  onError?: (error: any) => void
}

export function useCustomChat(options: ChatOptions) {
  const { chatStream } = useDummyData()

  const messages = ref<ChatMessage[]>(options.initialMessages || [])
  const input = ref('')
  const status = ref<'ready' | 'streaming' | 'error'>('ready')
  const error = ref<Error | null>(null)

  const currentAssistantMessage = ref('')
  let currentAssistantMessageId = ''

  async function handleSubmit(e?: Event) {
    e?.preventDefault()

    if (!input.value.trim() || status.value === 'streaming') return

    const userMessage: ChatMessage = {
      id: generateUUID(),
      content: input.value,
      role: 'user',
      createdAt: new Date().toISOString()
    }

    messages.value.push(userMessage)

    // Create assistant message placeholder
    currentAssistantMessageId = generateUUID()
    const assistantMessage: ChatMessage = {
      id: currentAssistantMessageId,
      content: '',
      role: 'assistant',
      createdAt: new Date().toISOString()
    }

    messages.value.push(assistantMessage)
    currentAssistantMessage.value = ''

    const userInput = input.value
    input.value = ''
    status.value = 'streaming'
    error.value = null

    try {
      await mockChatStream(
        options.id,
        [userMessage],
        (chunk: string) => {
          currentAssistantMessage.value += chunk
          // Update the assistant message in the messages array
          const assistantMsgIndex = messages.value.findIndex(msg => msg.id === currentAssistantMessageId)
          if (assistantMsgIndex > -1) {
            messages.value[assistantMsgIndex].content = currentAssistantMessage.value
          }
        },
        () => {
          status.value = 'ready'
          currentAssistantMessage.value = ''

          // Trigger response callback if provided
          if (options.onResponse) {
            options.onResponse({
              headers: { get: () => null }
            })
          }
        }
      )
    } catch (err) {
      status.value = 'error'
      error.value = err as Error

      // Remove the failed assistant message
      const assistantMsgIndex = messages.value.findIndex(msg => msg.id === currentAssistantMessageId)
      if (assistantMsgIndex > -1) {
        messages.value.splice(assistantMsgIndex, 1)
      }

      if (options.onError) {
        options.onError(err)
      }
    }
  }

  function stop() {
    status.value = 'ready'
    // In a real implementation, we would cancel the streaming request here
  }

  return {
    messages: readonly(messages),
    input,
    handleSubmit,
    stop,
    status: readonly(status),
    error: readonly(error)
  }
}
