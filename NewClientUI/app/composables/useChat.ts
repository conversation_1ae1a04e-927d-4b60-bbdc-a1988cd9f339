import { ref, computed, watch, nextTick } from 'vue'
import { useAppStore } from '~/stores/app'
import { useCollectionsStore } from '~/stores/collections'
import { useStreamingChat } from '~/composables/useStreamingChat'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  isStreaming?: boolean
  queryId?: string
  isFavorite?: boolean
  retrievedDocs?: any[]
  sourceCount?: number
}

export const useChat = () => {
  const loading = ref(false)
  const messages = ref<Message[]>([])
  const messagesContainer = ref<HTMLElement | null>(null)
  const streamingMessageId = ref<string | null>(null)

  const collectionsStore = useCollectionsStore()
  const { sendStreamingMessage, abortStream } = useStreamingChat()

  const checkSessionStatus = async () => {
    if (!collectionsStore.currentSession) {
      if (collectionsStore.unassignedSessions.length > 0
        || collectionsStore.favoriteSessions.length > 0
        || Object.keys(collectionsStore.folderSessions).length > 0) {
        const recentSession = collectionsStore.unassignedSessions[0]
          || collectionsStore.favoriteSessions[0]
          || Object.values(collectionsStore.folderSessions).flat()[0]

        if (recentSession) {
          await collectionsStore.selectSession(recentSession.id)
          await loadChatHistory()
        }
      }
    } else {
      await loadChatHistory()
    }
  }

  const loadChatHistory = async () => {
    if (!collectionsStore.currentSession) return

    const queries = await collectionsStore.fetchSessionQueries(collectionsStore.currentSession.id)

    messages.value = queries.flatMap(q => [
      {
        id: q.id + '-q',
        role: 'user' as const,
        content: q.content,
        queryId: q.id
      },
      {
        id: q.id + '-a',
        role: 'assistant' as const,
        content: q.answer,
        queryId: q.id,
        isFavorite: q.is_favorite,
        retrievedDocs: [],
        sourceCount: q.len_of_retrieved_docs
      }
    ])

    await scrollToBottom()
  }

  const scrollToBottom = async () => {
    await nextTick()
    if (messagesContainer.value) {
      messagesContainer.value.scrollTo({
        top: messagesContainer.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  }

  const sendMessage = async (messageText: string) => {
    if (!messageText.trim() || loading.value) return

    if (!collectionsStore.currentSession) {
      try {
        const session = await collectionsStore.createSession('', undefined)
        if (!session || !session.id) {
          console.error('Failed to create session')
          return
        }
        messages.value = []
      } catch (error) {
        console.error('Error creating session:', error)
        return
      }
    }

    if (!collectionsStore.currentCollection) {
      console.error('No collection selected')
      return
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: messageText
    }
    messages.value.push(userMessage)

    const assistantMessageId = (Date.now() + 1).toString()
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      isStreaming: true
    }
    messages.value.push(assistantMessage)
    streamingMessageId.value = assistantMessageId
    loading.value = true

    await scrollToBottom()

    await sendStreamingMessage({
      collectionId: collectionsStore.currentCollection.id,
      sessionId: collectionsStore.currentSession.id,
      content: messageText,
      customPrompt: collectionsStore.currentCollection.custom_config?.system_prompt,
      topK: 10,
      searchMethod: collectionsStore.currentCollection.custom_config?.method,
      markdownSupport: false,
      onChunk: (chunk) => {
        const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
        if (messageIndex !== -1 && chunk.content) {
          messages.value[messageIndex].content += chunk.content

          if (chunk.query_id) {
            messages.value[messageIndex].queryId = chunk.query_id
          }
          if (chunk.retrieved_docs) {
            messages.value[messageIndex].retrievedDocs = chunk.retrieved_docs
          }

          // Auto-scroll during streaming
          if (messagesContainer.value) {
            const isAtBottom = messagesContainer.value.scrollHeight - messagesContainer.value.scrollTop
              <= messagesContainer.value.clientHeight + 100
            if (isAtBottom) {
              messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
            }
          }
        }
      },
      onComplete: async (fullContent, headers) => {
        const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
        if (messageIndex !== -1) {
          messages.value[messageIndex].isStreaming = false
        }
        streamingMessageId.value = null
        loading.value = false

        // Check if we got a session ID from headers (for new sessions)
        if (headers && !collectionsStore.currentSession?.id) {
          const newSessionId = headers.get('X-Sessions-Id') || headers.get('x-sessions-id')
          if (newSessionId) {
            await collectionsStore.selectSession(newSessionId)
          }
        }

        await loadChatHistory()
      },
      onError: (error) => {
        console.error('Streaming error:', error)

        const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
        if (messageIndex !== -1) {
          messages.value[messageIndex].content = 'Sorry, an error occurred while processing your request. Please try again.'
          messages.value[messageIndex].isStreaming = false
        }
        streamingMessageId.value = null
        loading.value = false
      }
    })
  }

  // Stop streaming
  const stopStreaming = () => {
    if (streamingMessageId.value) {
      abortStream()
      const messageIndex = messages.value.findIndex(m => m.id === streamingMessageId.value)
      if (messageIndex !== -1) {
        messages.value[messageIndex].isStreaming = false
      }
      streamingMessageId.value = null
      loading.value = false
    }
  }

  // Handle new session created event
  const handleNewSessionCreated = (event: CustomEvent) => {
    const sessionId = event.detail?.sessionId
    if (sessionId) {
      messages.value = []
    }
  }

  watch(messages, async () => {
    if (messagesContainer.value) {
      const isNearBottom = messagesContainer.value.scrollHeight - messagesContainer.value.scrollTop
        <= messagesContainer.value.clientHeight + 200

      if (isNearBottom) {
        await scrollToBottom()
      }
    }
  }, { deep: true })

  // Watch for session changes
  watch(() => collectionsStore.currentSession?.id, async (newSessionId, oldSessionId) => {
    if (newSessionId && newSessionId !== oldSessionId) {
      await loadChatHistory()
    }
  })

  return {
    loading,
    messages,
    messagesContainer,
    streamingMessageId,
    checkSessionStatus,
    loadChatHistory,
    sendMessage,
    stopStreaming,
    scrollToBottom,
    handleNewSessionCreated
  }
}
