import { ref, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '~/stores/app'

export const useScreenSize = () => {
  const shouldAutoHideSidebar = ref(false)
  const appStore = useAppStore()

  const checkScreenSize = () => {
    if (typeof window !== 'undefined') {
      shouldAutoHideSidebar.value = window.innerWidth < 1200

      if (shouldAutoHideSidebar.value && appStore.isSidebarVisible) {
        appStore.isSidebarVisible = false
      }
    }
  }

  const initScreenSizeListener = () => {
    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
  }

  const removeScreenSizeListener = () => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', checkScreenSize)
    }
  }

  onMounted(() => {
    initScreenSizeListener()
  })

  onUnmounted(() => {
    removeScreenSizeListener()
  })

  return {
    shouldAutoHideSidebar,
    checkScreenSize,
    initScreenSizeListener,
    removeScreenSizeListener
  }
}
