import type { Session } from '~/stores/collections'
import { useCollectionsStore } from '~/stores/collections'
import { useAppStore } from '~/stores/app'
export interface DragDropState {
  isDragging: boolean
  draggedSession: Session | null
  draggedFromFolder: string | null
  dropTargetId: string | null
  dropTargetType: 'folder' | 'favorites' | 'unassigned' | null
}

export const useDragDrop = () => {
  const collectionsStore = useCollectionsStore()
  const appStore = useAppStore()
  
  const dragState = reactive<DragDropState>({
    isDragging: false,
    draggedSession: null,
    draggedFromFolder: null,
    dropTargetId: null,
    dropTargetType: null
  })

  // Find which folder a session belongs to
  const findSessionFolder = (sessionId: string): string | null => {
    if (!collectionsStore.sessions.folders) return null
    
    for (const [folderName, sessions] of Object.entries(collectionsStore.sessions.folders)) {
      if (Array.isArray(sessions) && sessions.some(s => s.id === sessionId)) {
        if (folderName === 'favorites' || folderName === 'unassigned') {
          return folderName
        }
        return folderName
      }
    }
    return null
  }

  // Start dragging a session
  const onDragStart = (event: DragEvent, session: Session) => {
    if (!event.dataTransfer) return
    
    dragState.isDragging = true
    dragState.draggedSession = session
    dragState.draggedFromFolder = findSessionFolder(session.id)
    
    // Set drag effect and data
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/plain', session.id)
    
    // Add dragging class to the element
    const target = event.target as HTMLElement
    target.classList.add('dragging')
    
    // Create custom drag image
    const dragImage = target.cloneNode(true) as HTMLElement
    dragImage.style.position = 'absolute'
    dragImage.style.top = '-1000px'
    dragImage.style.opacity = '0.8'
    dragImage.style.transform = 'rotate(2deg)'
    document.body.appendChild(dragImage)
    event.dataTransfer.setDragImage(dragImage, event.offsetX, event.offsetY)
    
    // Clean up drag image after a moment
    setTimeout(() => {
      document.body.removeChild(dragImage)
    }, 0)
  }

  // Handle drag over for drop zones
  const onDragOver = (event: DragEvent, targetId: string, targetType: 'folder' | 'favorites' | 'unassigned') => {
    event.preventDefault()
    
    if (!dragState.isDragging || !dragState.draggedSession) return
    
    // Don't allow dropping on the same location
    if (dragState.draggedFromFolder === targetId) {
      event.dataTransfer!.dropEffect = 'none'
      return
    }
    
    event.dataTransfer!.dropEffect = 'move'
    dragState.dropTargetId = targetId
    dragState.dropTargetType = targetType
    
    // Add hover effect to drop zone
    const target = event.currentTarget as HTMLElement
    target.classList.add('drag-over')
  }

  // Handle drag enter for visual feedback
  const onDragEnter = (event: DragEvent) => {
    event.preventDefault()
    const target = event.currentTarget as HTMLElement
    target.classList.add('drag-enter')
  }

  // Handle drag leave to remove visual feedback
  const onDragLeave = (event: DragEvent) => {
    const target = event.currentTarget as HTMLElement
    target.classList.remove('drag-over', 'drag-enter')
    
    // Check if we're really leaving the drop zone
    const relatedTarget = event.relatedTarget as HTMLElement
    if (target.contains(relatedTarget)) return
    
    dragState.dropTargetId = null
    dragState.dropTargetType = null
  }

  // Handle drop
  const onDrop = async (event: DragEvent, targetId: string, targetType: 'folder' | 'favorites' | 'unassigned') => {
    event.preventDefault()
    event.stopPropagation()
    
    const target = event.currentTarget as HTMLElement
    target.classList.remove('drag-over', 'drag-enter')
    
    if (!dragState.draggedSession || !dragState.isDragging) return
    
    const sessionId = dragState.draggedSession.id
    const fromFolder = dragState.draggedFromFolder
    
    // Don't do anything if dropping in the same location
    if (fromFolder === targetId) {
      resetDragState()
      return
    }
    
    try {
      // Handle different drop scenarios
      if (targetType === 'favorites') {
        // Add to favorites
        await collectionsStore.updateFavoriteStatus(sessionId, true)
        appStore.addNotification({
          type: 'success',
          title: 'Session added to favorites'
        })
      } else if (targetType === 'unassigned') {
        // Remove from folder or favorites
        if (fromFolder === 'favorites') {
          await collectionsStore.updateFavoriteStatus(sessionId, false)
          appStore.addNotification({
            type: 'success',
            title: 'Session removed from favorites'
          })
        } else if (fromFolder && fromFolder !== 'unassigned') {
          await collectionsStore.removeSessionFromFolder(sessionId, fromFolder)
          appStore.addNotification({
            type: 'success',
            title: 'Session moved to unassigned'
          })
        }
      } else if (targetType === 'folder' && targetId) {
        // Move to a specific folder
        if (fromFolder === 'favorites') {
          // First remove from favorites
          await collectionsStore.updateFavoriteStatus(sessionId, false)
        } else if (fromFolder && fromFolder !== 'unassigned' && fromFolder !== 'favorites') {
          // Remove from current folder first
          await collectionsStore.removeSessionFromFolder(sessionId, fromFolder)
        }
        
        // Then add to new folder
        await collectionsStore.moveSessionToFolder(sessionId, targetId)
        appStore.addNotification({
          type: 'success',
          title: `Session moved to ${targetId}`
        })
      }
    } catch (error) {
      console.error('Error handling drop:', error)
      appStore.addNotification({
        type: 'error',
        title: 'Failed to move session',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
    
    resetDragState()
  }

  // End drag
  const onDragEnd = (event: DragEvent) => {
    const target = event.target as HTMLElement
    target.classList.remove('dragging')
    
    // Clean up any remaining drag-over classes
    document.querySelectorAll('.drag-over, .drag-enter').forEach(el => {
      el.classList.remove('drag-over', 'drag-enter')
    })
    
    resetDragState()
  }

  // Reset drag state
  const resetDragState = () => {
    dragState.isDragging = false
    dragState.draggedSession = null
    dragState.draggedFromFolder = null
    dragState.dropTargetId = null
    dragState.dropTargetType = null
  }

  return {
    dragState: readonly(dragState),
    onDragStart,
    onDragOver,
    onDragEnter,
    onDragLeave,
    onDrop,
    onDragEnd
  }
}
