import { AUTH_CONFIG } from '~/config/api'
import { useAuth } from '~/composables/useAuth'

export interface StreamMessage {
  content: string
  retrieved_docs?: any[]
  docs_mmr?: any[]
  query_id?: string
  session_id?: string
  is_web_searched?: boolean
}

export interface ChatStreamOptions {
  collectionId: string
  sessionId?: string
  content: string
  customPrompt?: string
  topK?: number
  searchMethod?: string
  markdownSupport?: boolean
  onChunk?: (chunk: StreamMessage) => void
  onComplete?: (fullContent: string, headers?: Headers) => void
  onError?: (error: Error) => void
}

export const useStreamingChat = () => {
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const currentMessage = ref('')
  const abortController = ref<AbortController | null>(null)
  const { getToken } = useAuth()

  const sendStreamingMessage = async (options: ChatStreamOptions) => {
    const {
      collectionId,
      sessionId,
      content,
      onChunk,
      onComplete,
      onError
    } = options

    loading.value = true
    error.value = null
    currentMessage.value = ''

    abortController.value = new AbortController()

    try {
      const requestBody = {
        content,
        session_id: sessionId,
        inline_pdf_content: ''
      }

      // Get auth token
      const token = getToken()
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...(token && { [AUTH_CONFIG.TOKEN_HEADER]: `${AUTH_CONFIG.TOKEN_PREFIX} ${token}` })
      }

      const endpoint = `/api/query/collection/${collectionId}/query_stream`

      console.log(`[Streaming] Sending request to: ${endpoint}`)
      console.log('[Streaming] Request body:', requestBody)

      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: abortController.value?.signal
      })

      console.log(`[Streaming] Response status: ${response.status}`)
      console.log('[Streaming] Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorText = await response.text()
        console.error('[Streaming] Error response:', errorText)
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      const decoder = new TextDecoder()
      const responseHeaders = response.headers
      let buffer = ''

      // Use async iteration for cleaner stream processing
      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) break

          // Decode the chunk
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          console.log(`[Streaming] Received chunk: ${chunk.substring(0, 100)}...`)

          // Process complete JSON objects in buffer
          buffer = processChunksInBuffer(buffer, onChunk)
        }
      } finally {
        reader.releaseLock()
      }

      // Process any remaining buffer
      if (buffer.trim()) {
        processRemainingBuffer(buffer, onChunk)
      }

      // Call complete callback with full message and headers
      onComplete?.(currentMessage.value, responseHeaders)
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('Stream aborted')
      } else {
        const errorObj = err instanceof Error ? err : new Error(String(err))
        error.value = errorObj
        onError?.(errorObj)
      }
    } finally {
      loading.value = false
      abortController.value = null
    }
  }

  const processChunksInBuffer = (buffer: string, onChunk?: (chunk: StreamMessage) => void): string => {
    try {
      while (true) {
        const sepIndex = buffer.indexOf('}{')
        if (sepIndex === -1) break

        const jsonStr = buffer.slice(0, sepIndex + 1)
        buffer = buffer.slice(sepIndex + 1)

        try {
          const data: StreamMessage = JSON.parse(jsonStr)

          if (data.content) {
            currentMessage.value += data.content
          }

          onChunk?.(data)
        } catch (e) {
          console.error('Error parsing JSON chunk:', e, jsonStr)
        }
      }
    } catch (e) {
      console.error('Error in chunk processing:', e)
    }
    return buffer
  }

  const processRemainingBuffer = (buffer: string, onChunk?: (chunk: StreamMessage) => void) => {
    try {
      const data: StreamMessage = JSON.parse(buffer)
      if (data.content) {
        currentMessage.value += data.content
      }
      onChunk?.(data)
    } catch (e) {
      console.error('Error parsing final buffer:', e, buffer)
    }
  }

  const abortStream = () => {
    abortController.value?.abort()
    abortController.value = null
    loading.value = false
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    currentMessage: readonly(currentMessage),
    sendStreamingMessage,
    abortStream
  }
}
