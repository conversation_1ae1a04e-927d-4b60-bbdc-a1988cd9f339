<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import { useCollectionsStore } from '~/stores/collections'
import { useChat } from '~/composables/useChat'
import { useScreenSize } from '~/composables/useScreenSize'

const appStore = useAppStore()
const collectionsStore = useCollectionsStore()
const router = useRouter()

const isSwitchingCollections = computed(() => collectionsStore.isLoadingSessions)

const {
  loading,
  messages,
  messagesContainer,
  checkSessionStatus,
  sendMessage,
  handleNewSessionCreated,
  loadChatHistory
} = useChat()

const { removeScreenSizeListener } = useScreenSize()

// Message action handlers and sources overlay state
const showSourcesOverlay = ref(false)
const activeSources = ref<any[]>([])
const isLoadingSources = ref(false)

const onShowSources = async (payload: { id?: string, sources?: any[] }) => {
  const message = messages.value.find(m => m.id === payload.id)
  if (!message?.queryId) {
    console.error('No queryId found for message')
    return
  }

  showSourcesOverlay.value = true
  isLoadingSources.value = true
  activeSources.value = []

  try {
    const { get } = useAuthFetch()
    const data = await get<{ retrieved_docs: any[] }>(
      `/api/query/get_relevant_docs_by_query_id/${message.queryId}`
    )

    if (data?.retrieved_docs) {
      activeSources.value = data.retrieved_docs.map((doc: any) => {
        const documentInfo = Array.isArray(doc) ? doc[0] : doc
        const score = Array.isArray(doc) ? doc[1] : null

        // Extract metadata
        const metadata = documentInfo.metadata || {}
        const source = metadata.source || ''

        // Extract filename from source
        let fileName = ''
        if (source.startsWith('http://') || source.startsWith('https://')) {
          fileName = source
        } else if (source) {
          fileName = source.split('/').pop() || source
        }

        const page = metadata.page || metadata.page_num || 0

        return {
          title: metadata.title || '',
          fileName,
          content: documentInfo.page_content || '',
          page,
          score,
          raw: documentInfo
        }
      })
    }
  } catch (error) {
    console.error('Error fetching relevant documents:', error)
    activeSources.value = [{
      title: 'Error',
      fileName: '',
      content: 'Fehler beim Abrufen der Dokumente.',
      page: 0
    }]
  } finally {
    isLoadingSources.value = false
  }
}
const onCopy = (_id?: string) => {}
const onSpeak = (_id?: string) => {}
const onStopSpeak = (_id?: string) => {}

// Handle refinement action
const onRefine = async (action: string, messageId?: string) => {
  if (!messageId || !collectionsStore.currentCollection || !collectionsStore.currentSession) {
    console.error('Missing required data for refinement')
    return
  }

  // Find the message and its queryId
  const messageIndex = messages.value.findIndex(m => m.id === messageId)
  if (messageIndex === -1) {
    console.error('Message not found')
    return
  }

  const message = messages.value[messageIndex]
  if (!message.queryId) {
    console.error('No queryId found for message')
    return
  }

  // Set the message to streaming state
  messages.value[messageIndex].content = ''
  messages.value[messageIndex].isStreaming = true

  try {
    const { fetchWithAuth } = useAuthFetch()
    const payload = {
      session_id: collectionsStore.currentSession.id,
      query_id: message.queryId,
      adjust_type: action,
      stream: true
    }

    const response = await fetchWithAuth(
      `/api/query/collection/${collectionsStore.currentCollection.id}/query_update`,
      {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      }
    )

    if (!response.ok) {
      throw new Error(`Request failed with status: ${response.status}`)
    }

    if (!response.body) {
      throw new Error('No readable stream from server')
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let partialChunk = ''

    // Read stream chunks
    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      partialChunk += decoder.decode(value, { stream: true })

      // Try to parse complete JSON objects from the stream
      try {
        while (true) {
          const sepIndex = partialChunk.indexOf('}{')
          if (sepIndex === -1) break

          const jsonStr = partialChunk.slice(0, sepIndex + 1)
          partialChunk = partialChunk.slice(sepIndex + 1)

          const parsed = JSON.parse(jsonStr)
          const chunkContent = parsed.content || ''

          // Update message content
          messages.value[messageIndex].content += chunkContent

          // Update queryId if provided
          if (parsed.query_id) {
            messages.value[messageIndex].queryId = parsed.query_id
          }

          // Update retrieved docs if provided
          if (parsed.retrieved_docs) {
            messages.value[messageIndex].retrievedDocs = parsed.retrieved_docs
          }

          // Auto-scroll during streaming
          if (messagesContainer.value) {
            const isAtBottom = messagesContainer.value.scrollHeight - messagesContainer.value.scrollTop
              <= messagesContainer.value.clientHeight + 100
            if (isAtBottom) {
              messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
            }
          }
        }
      } catch {
        // Wait for more data if JSON parsing fails
      }
    }

    // Try to parse any remaining chunk
    if (partialChunk.trim()) {
      try {
        const lastObj = JSON.parse(partialChunk)
        const chunkContent = lastObj.content || ''
        messages.value[messageIndex].content += chunkContent

        if (lastObj.query_id) {
          messages.value[messageIndex].queryId = lastObj.query_id
        }
        if (lastObj.retrieved_docs) {
          messages.value[messageIndex].retrievedDocs = lastObj.retrieved_docs
        }
      } catch {
        // Ignore parsing errors for incomplete JSON
      }
    }

    // Mark streaming as complete
    messages.value[messageIndex].isStreaming = false

    // Refresh chat history to sync with backend
    await loadChatHistory()
  } catch (error) {
    console.error('[onRefine] Error:', error)
    messages.value[messageIndex].content = 'Fehler beim Verfeinern der Antwort. Bitte versuchen Sie es später erneut.'
    messages.value[messageIndex].isStreaming = false
  }
}

const onToggleFavorite = async (messageId?: string, nextValue?: boolean) => {
  if (!messageId || !collectionsStore.currentCollection) {
    console.error('Missing required data for favorite toggle')
    return
  }

  // Find the message to get its queryId
  const messageIndex = messages.value.findIndex(m => m.id === messageId)
  if (messageIndex === -1) {
    console.error('Message not found')
    return
  }

  const message = messages.value[messageIndex]
  if (!message.queryId) {
    console.error('No queryId found for message')
    return
  }

  try {
    const { put } = useAuthFetch()
    const response = await put<{ is_favorite: boolean }>(
      `/api/query/${message.queryId}/toggle_favorite/${collectionsStore.currentCollection.id}`
    )

    if (response) {
      messages.value[messageIndex].isFavorite = response.is_favorite

      await loadChatHistory()
    }
  } catch (error) {
    console.error('Error updating favorite status:', error)
    // Revert the UI state on error
    const messageEl = messages.value[messageIndex]
    if (messageEl) {
      // Force re-render to revert the star state
      messageEl.isFavorite = !nextValue
    }
  }
}

// Computed property for welcome message
const welcomeMessage = computed(() => {
  const collection = collectionsStore.currentCollection
  if (collection?.description) {
    return `Willkommen bei ${collection.name}!\n\n${collection.description}`
  }
  return 'Willkommen'
})

onMounted(async () => {
  window.addEventListener('session-created', handleNewSessionCreated)

  if (!collectionsStore.currentCollection) {
    const savedCollection = sessionStorage.getItem('selectedCollection')
    if (savedCollection) {
      try {
        const collection = JSON.parse(savedCollection)
        collectionsStore.selectCollection(collection)
      } catch (error) {
        console.error('Error parsing saved collection:', error)
        router.push('/')
        return
      }
    } else {
      router.push('/')
      return
    }
  }

  await checkSessionStatus()
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('session-created', handleNewSessionCreated)
  }
  removeScreenSizeListener()
})

definePageMeta({
  layout: 'general'
})
</script>

<template>
  <div class="chat-page">
    <div class="mesh-box" />

    <div class="chat-header p-2 w-full flex justify-between items-center" :class="{ 'sidebar-open': appStore.isSidebarVisible }">
      <img
        src="/images/fbeta.png"
        alt="fbeta"
        class="md:h-10 mb-8 ml-8 "
        :class=" { 'ml-[18%]': appStore.isSidebarVisible }"
      >
      <div class="mr-4">
        <UserMenu collapsed />
      </div>
    </div>

    <ChatSidebar />

    <div class="chat-main" :class="{ 'sidebar-open': appStore.isSidebarVisible }">
      <!-- Loader overlay during collection/session switching -->
      <div v-if="isSwitchingCollections" class="chat-loader-overlay">
        <UIcon name="i-lucide-loader-2" class="w-10 h-10 text-white/60 animate-spin" />
      </div>
      <div ref="messagesContainer" class="chat-messages" :class="{ 'blur-sm pointer-events-none': isSwitchingCollections }">
        <div v-if="messages.length === 0" class="welcome-container">
          <ChatBubble
            :message="welcomeMessage"
            :is-user="true"
          />
        </div>

        <div v-for="message in messages" :key="message.id">
          <ChatBubble
            :message="message.content"
            :is-user="message.role === 'user'"
            :is-streaming="message.isStreaming"
            :source-count="message.sourceCount || message.retrievedDocs?.length || 0"
            :message-id="message.id"
            :retrieved-docs="message.retrievedDocs"
            :is-favorite="message.isFavorite"
            @copy="onCopy"
            @speak="onSpeak"
            @stop-speak="onStopSpeak"
            @toggle-favorite="onToggleFavorite"
            @show-sources="onShowSources"
            @refine="onRefine"
          />
        </div>
      </div>
    </div>

    <!-- Sources overlay -->
    <div v-if="showSourcesOverlay" class="sources-overlay" @click.self="showSourcesOverlay = false">
      <UiLiquidGlassCard class="sources-card" size="sm" rounded>
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-white text-lg font-semibold mx-4">
            Relevante Dokumente
          </h3>
          <UButton
            icon="i-lucide-x"
            variant="ghost"
            size="sm"
            class="text-white/80 hover:text-white"
            @click="showSourcesOverlay = false"
          />
        </div>

        <!-- Loading state -->
        <div v-if="isLoadingSources" class="flex items-center justify-center py-8">
          <UIcon name="i-lucide-loader-2" class="w-8 h-8 text-white/60 animate-spin" />
        </div>

        <!-- No sources message -->
        <div v-else-if="activeSources.length === 0" class="text-white/60 text-sm py-4 text-center">
          Keine relevanten Dokumente gefunden.
        </div>

        <!-- Sources list -->
        <div v-else class="sources-list max-h-[60vh] overflow-y-auto pr-2">
          <div
            v-for="(src, idx) in activeSources"
            :key="idx"
            class="source-item mb-4 pb-4 border-b border-white/10 last:border-b-0"
          >
            <!-- Title if available -->
            <div v-if="src.title" class="mb-2">
              <span class="text-white/60 text-xs font-semibold">Titel:</span>
              <p class="text-white/90 text-sm mt-1">
                {{ src.title }}
              </p>
            </div>

            <!-- Source file -->
            <div v-if="src.fileName" class="mb-2">
              <span class="text-white/60 text-xs font-semibold">Quelldatei:</span>
              <p class="text-white/90 text-sm mt-1">
                {{ src.fileName }}
              </p>
            </div>

            <!-- Content -->
            <div v-if="src.content" class="mb-2">
              <span class="text-white/60 text-xs font-semibold">Inhalt:</span>
              <p class="text-white/80 text-sm mt-1 leading-relaxed">
                {{ src.content }}
              </p>
            </div>

            <!-- Page number -->
            <div v-if="src.page !== undefined" class="mb-2">
              <span class="text-white/60 text-xs font-semibold">Seitenzahl:</span>
              <span class="text-white/90 text-sm ml-2">{{ src.page }}</span>
            </div>

            <!-- Score if available -->
            <div v-if="src.score !== null && src.score !== undefined" class="mt-2">
              <span class="text-white/60 text-xs font-semibold">Relevanz:</span>
              <span class="text-white/90 text-sm ml-2">{{ (src.score * 100).toFixed(1) }}%</span>
            </div>
          </div>
        </div>
      </UiLiquidGlassCard>
    </div>

    <div class="bottom-controls" :class="{ 'sidebar-open': appStore.isSidebarVisible }">
      <div class="controls-container">
        <UiLiquidGlassCard
          v-if="!appStore.isSidebarVisible"
          size="sm"
          rounded
          @click="appStore.toggleSidebar"
        >
          <UButton
            icon="i-lucide-menu"
            variant="link"
            size="lg"
            active-variant="link"
            class="text-secondary-custom hover:text-white"
          />
        </UiLiquidGlassCard>

        <UiLiquidGlassCard
          size="sm"
          rounded
          @click="appStore.toggleFavorites"
        >
          <UButton
            v-if="appStore.showFavorites"
            icon="i-lucide-message-circle"
            class="text-secondary-custom hover:text-white"
            variant="link"
            size="lg"
            active-variant="link"
          />
          <div v-else class="p-2">
            <img
              src="/images/star-filled.svg"
              alt="Favorites"
              class="w-4 h-4 opacity-70 hover:opacity-100 transition-opacity cursor-pointer"
            >
          </div>
        </UiLiquidGlassCard>
        <div class="flex-1">
          <ChatInput
            :loading="loading"
            class="responsive-input"
            @submit="sendMessage"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.chat-header {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
  transition: all 0.3s ease-in-out;
}

.chat-header.sidebar-open {
  left: calc(50% + 140px);
}

.chat-main {
  flex: 1;
  padding: 6rem 2rem 8rem 2rem;
  max-width: 75%;
  margin: 0 auto;
  width: 75%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-in-out;
  position: relative; /* for overlay positioning */
}

.chat-main.sidebar-open {
  margin-left: calc(50% + 160px);
  margin-right: auto;
  transform: translateX(-50%);
  width: 100%;
  max-width: 800px;
}

.bottom-controls {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 4rem);
  max-width: 65%;
  z-index: 10;
  transition: all 0.3s ease-in-out;
}

.bottom-controls.sidebar-open {
  left: calc(50% + 160px);
  width: calc(100% - 320px - 4rem);
}

.controls-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding-right: 1rem;
  margin-bottom: 2rem;
}

.welcome-container {
  margin-bottom: 2rem;
}

/* Custom scrollbar */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

@media (max-width: 1024px) {
  .chat-header.sidebar-open {
    left: 50%;
  }

  .chat-main.sidebar-open {
    margin-left: 0;
    margin-right: 0;
    transform: none;
    width: 100%;
  }

  .bottom-controls.sidebar-open {
    left: 50%;
    width: calc(100% - 4rem);
  }
}

.chat-loader-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.25);
  z-index: 2;
}

.sources-overlay {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.35);
  z-index: 50;
  padding: 1rem;
}

.sources-card {
  width: 100%;
  max-width: 640px;
  max-height: 70vh;
  overflow: hidden;
}

/* Custom scrollbar for sources list */
.sources-list::-webkit-scrollbar {
  width: 6px;
}

.sources-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.sources-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.sources-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

@media (max-width: 768px) {
  .chat-header {
    top: 1rem;
  }

  .chat-main {
    padding: 5rem 1rem 8rem 1rem;
  }

  .chat-messages {
    padding-right: 0.5rem;
  }

  .bottom-controls {
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    width: auto;
    transform: none;
  }

  .controls-container {
    gap: 0.5rem;
  }
}
</style>
