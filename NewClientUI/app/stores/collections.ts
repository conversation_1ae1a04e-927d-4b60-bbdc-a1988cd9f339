import { defineStore } from 'pinia'
import { useAuthFetch } from '~/composables/useAuthFetch'

export interface Collection {
  id: string
  name: string
  description: string
  iconKey?: string
}

export interface Session {
  id: string
  title: string
  collection_id: string
  created_at: string
  updated_at: string
  is_favorite?: boolean
  uploaded_pdfs?: string
  custom_config?: {
    system_prompt?: string
  }
  folder_name?: string
  queries?: Query[]
}

export interface Query {
  id: string
  content: string
  answer: string
  created_at: string
  session_id: string
  is_favorite?: boolean
  len_of_retrieved_docs?: number
}

export interface Folder {
  name: string
  sessions: Session[]
}

export interface SessionsData {
  folders: {
    favorites?: Session[]
    unassigned?: Session[]
    [key: string]: Session[] | undefined
  }
}

export const useCollectionsStore = defineStore('collections', {
  state: () => ({
    collections: [] as Collection[],
    currentCollection: null as Collection | null,
    sessions: {} as SessionsData,
    currentSession: null as Session | null,
    isLoadingCollections: false,
    isLoadingSessions: false,
    error: null as string | null,
    folders: [] as string[],
    selectedFolder: null as string | null
  }),

  getters: {
    hasCollections: state => state.collections.length > 0,

    favoriteSessions: state => state.sessions.folders?.favorites || [],

    unassignedSessions: state => state.sessions.folders?.unassigned || [],

    folderSessions: (state) => {
      if (!state.sessions.folders) return {}
      const { favorites, unassigned, ...folders } = state.sessions.folders
      return folders
    },

    currentSessionQueries: state => state.currentSession?.queries || []
  },

  actions: {
    async fetchCollections() {
      this.isLoadingCollections = true
      this.error = null

      try {
        const { get } = useAuthFetch()
        const data = await get<any>('/api/loader/collections')

        if (data && data.collections) {
          this.collections = data.collections

          // Store user info if available
          if (data.user_info?.email) {
            localStorage.setItem('user_email', data.user_info.email)
          }
        }
      } catch (error) {
        console.error('Error fetching collections:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch collections'
      } finally {
        this.isLoadingCollections = false
      }
    },

    selectCollection(collection: Collection) {
      this.currentCollection = collection
      // Fetch sessions for this collection
      this.fetchSessions(collection.id)
    },

    async fetchSessions(collectionId: string) {
      this.isLoadingSessions = true
      this.error = null

      try {
        const { get } = useAuthFetch()
        const data = await get<SessionsData>(`/api/query/get_sessions_by_collection_id/${collectionId}`)

        this.sessions = data || { folders: {} }

        // Extract folder names
        if (data?.folders) {
          const folderNames = Object.keys(data.folders).filter(
            key => key !== 'favorites' && key !== 'unassigned'
          )
          this.folders = folderNames
        }
      } catch (error) {
        console.error('Error fetching sessions:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch sessions'
      } finally {
        this.isLoadingSessions = false
      }
    },

    async createSession(goal: string = '', folderName?: string) {
      if (!this.currentCollection) {
        throw new Error('No collection selected')
      }

      try {
        const { post } = useAuthFetch()

        // Create the session
        const sessionData = await post<Session>(
          `/api/query/create_session/${this.currentCollection.id}`,
          { session_goal: goal }
        )

        // If folder name is provided, assign session to folder
        if (folderName && sessionData.id) {
          await post(
            `/api/query/move_session_to_folder`,
            null,
            {
              params: {
                folder_name: folderName,
                session_id: sessionData.id,
                collection_id: this.currentCollection.id
              }
            }
          )
        }

        // Refresh sessions list
        await this.fetchSessions(this.currentCollection.id)

        // Select the new session
        this.selectSession(sessionData.id)

        return sessionData
      } catch (error) {
        console.error('Error creating session:', error)
        throw error
      }
    },

    async createFolder(folderName: string) {
      if (!this.currentCollection) {
        throw new Error('No collection selected')
      }

      try {
        const { post } = useAuthFetch()
        await post(
          `/api/query/create_folder/${this.currentCollection.id}/${encodeURIComponent(folderName)}`,
          null
        )

        // Refresh sessions to get updated folder list
        await this.fetchSessions(this.currentCollection.id)
      } catch (error) {
        console.error('Error creating folder:', error)
        throw error
      }
    },

    async fetchSessionQueries(sessionId: string) {
      try {
        const { get } = useAuthFetch()
        const queries = await get<Query[]>(`/api/query/get_queries_by_session_id/${sessionId}`)

        // Update the current session with queries
        if (this.currentSession && this.currentSession.id === sessionId) {
          this.currentSession.queries = queries || []
        }

        return queries || []
      } catch (error) {
        console.error('Error fetching session queries:', error)
        return []
      }
    },

    async selectSession(sessionId: string) {
      try {
        // Find the session in our data
        let foundSession: Session | undefined

        // Check all folders for the session
        if (this.sessions.folders) {
          for (const folder of Object.values(this.sessions.folders)) {
            if (Array.isArray(folder)) {
              foundSession = folder.find(s => s.id === sessionId)
              if (foundSession) break
            }
          }
        }

        if (foundSession) {
          this.currentSession = foundSession
          // Fetch session queries
          await this.fetchSessionQueries(sessionId)
        }
      } catch (error) {
        console.error('Error selecting session:', error)
      }
    },

    async renameSession(sessionId: string, newTitle: string) {
      try {
        const { put } = useAuthFetch()
        await put(`/api/query/update_session/${sessionId}`, { title: newTitle })

        // Refresh sessions
        if (this.currentCollection) {
          await this.fetchSessions(this.currentCollection.id)
        }
      } catch (error) {
        console.error('Error renaming session:', error)
        throw error
      }
    },

    async deleteSession(sessionId: string) {
      try {
        const { del } = useAuthFetch()
        await del(`/api/query/delete_session/${sessionId}`)

        // Clear current session if it's the one being deleted
        if (this.currentSession?.id === sessionId) {
          this.currentSession = null
        }

        // Refresh sessions
        if (this.currentCollection) {
          await this.fetchSessions(this.currentCollection.id)
        }
      } catch (error) {
        console.error('Error deleting session:', error)
        throw error
      }
    },

    async updateFavoriteStatus(sessionId: string, isFavorite: boolean) {
      try {
        const { put } = useAuthFetch()
        await put(`/api/query/update_favorite_status/${sessionId}?is_favorite=${isFavorite}`)

        // Refresh sessions
        if (this.currentCollection) {
          await this.fetchSessions(this.currentCollection.id)
        }
      } catch (error) {
        console.error('Error updating favorite status:', error)
        throw error
      }
    },

    async skipChatGoals(sessionId: string) {
      try {
        const { post } = useAuthFetch()
        await post(`/api/query/skip_chat_goals/${sessionId}`, {})
      } catch (error) {
        console.error('Error skipping chat goals:', error)
        throw error
      }
    },

    async checkChatGoalSkipped(sessionId: string): Promise<boolean> {
      try {
        const { get } = useAuthFetch()
        const data = await get<{ skipped: boolean }>(`/api/query/is_chat_goal_skipped/${sessionId}`)
        return data?.skipped || false
      } catch (error) {
        console.error('Error checking chat goal status:', error)
        return false
      }
    },

    async getSupplierFolders() {
      if (!this.currentCollection) return []

      try {
        const { get } = useAuthFetch()
        const data = await get<{ folders: Record<string, any> }>(
          `/api/query/list_folders/collection_id/${this.currentCollection.id}`
        )
        return Object.keys(data?.folders || {})
      } catch (error) {
        console.error('Error fetching folders:', error)
        return []
      }
    },

    clearCurrentSession() {
      this.currentSession = null
    },

    clearAll() {
      this.collections = []
      this.currentCollection = null
      this.sessions = { folders: {} }
      this.currentSession = null
      this.folders = []
      this.selectedFolder = null
      this.error = null
    }
  }
})
