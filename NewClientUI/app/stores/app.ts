import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    isSidebarVisible: true,
    showFavorites: false,
    showUserMenu: false,
    activeModal: null as string | null,
    isLoading: false,
    notifications: [] as Array<{
      id: string
      type: 'success' | 'error' | 'warning' | 'info'
      title: string
      message?: string
      duration?: number
    }>
  }),
  actions: {
    toggleSidebar() {
      this.isSidebarVisible = !this.isSidebarVisible
    },
    toggleFavorites() {
      this.showFavorites = !this.showFavorites
    },
    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu
    },
    closeUserMenu() {
      this.showUserMenu = false
    },
    openModal(modalId: string) {
      this.activeModal = modalId
    },
    closeModal() {
      this.activeModal = null
    },
    setLoading(loading: boolean) {
      this.isLoading = loading
    },
    addNotification(notification: Omit<typeof this.notifications[0], 'id'>) {
      const id = Date.now().toString()
      this.notifications.push({ ...notification, id })

      // Auto-remove after duration
      const duration = notification.duration || 5000
      setTimeout(() => {
        this.removeNotification(id)
      }, duration)
    },
    removeNotification(id: string) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    }
  }
})
