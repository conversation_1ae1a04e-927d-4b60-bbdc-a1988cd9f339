import { defineStore } from 'pinia'
import { ref } from 'vue'

interface DialogOptions {
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
  placeholder?: string
  initialValue?: string
  required?: boolean
}

export const useDialogStore = defineStore('dialog', () => {
  // State
  const isOpen = ref(false)
  const type = ref<'alert' | 'confirm' | 'prompt'>('alert')
  const title = ref('')
  const message = ref('')
  const confirmText = ref('OK')
  const cancelText = ref('Abbrechen')
  const placeholder = ref('')
  const initialValue = ref('')
  const required = ref(false)
  const showCancel = ref(true)

  // Callbacks stored as refs
  const onConfirm = ref<((value?: string) => void) | null>(null)
  const onCancel = ref<(() => void) | null>(null)

  // Actions
  const alert = (alertMessage: string, alertTitle?: string): Promise<void> => {
    return new Promise((resolve) => {
      isOpen.value = true
      type.value = 'alert'
      title.value = alertTitle || ''
      message.value = alertMessage
      confirmText.value = 'OK'
      cancelText.value = 'Abbrechen'
      placeholder.value = ''
      initialValue.value = ''
      required.value = false
      showCancel.value = false

      onConfirm.value = () => {
        isOpen.value = false
        resolve()
      }
      onCancel.value = null
    })
  }

  const confirm = (options: DialogOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      isOpen.value = true
      type.value = 'confirm'
      title.value = options.title || ''
      message.value = options.message || ''
      confirmText.value = options.confirmText || 'Ja'
      cancelText.value = options.cancelText || 'Nein'
      placeholder.value = ''
      initialValue.value = ''
      required.value = false
      showCancel.value = true

      onConfirm.value = () => {
        isOpen.value = false
        resolve(true)
      }
      onCancel.value = () => {
        isOpen.value = false
        resolve(false)
      }
    })
  }

  const prompt = (options: DialogOptions): Promise<string | null> => {
    return new Promise((resolve) => {
      isOpen.value = true
      type.value = 'prompt'
      title.value = options.title || ''
      message.value = options.message || ''
      confirmText.value = options.confirmText || 'OK'
      cancelText.value = options.cancelText || 'Abbrechen'
      placeholder.value = options.placeholder || ''
      initialValue.value = options.initialValue || ''
      required.value = options.required ?? false
      showCancel.value = true

      onConfirm.value = (value?: string) => {
        isOpen.value = false
        resolve(value || null)
      }
      onCancel.value = () => {
        isOpen.value = false
        resolve(null)
      }
    })
  }

  const handleConfirm = (value?: string) => {
    if (onConfirm.value) {
      onConfirm.value(value)
    }
  }

  const handleCancel = () => {
    if (onCancel.value) {
      onCancel.value()
    } else {
      isOpen.value = false
    }
  }

  return {
    // State
    isOpen,
    type,
    title,
    message,
    confirmText,
    cancelText,
    placeholder,
    initialValue,
    required,
    showCancel,

    // Actions
    alert,
    confirm,
    prompt,
    handleConfirm,
    handleCancel
  }
})
