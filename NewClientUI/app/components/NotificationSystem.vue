<template>
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <TransitionGroup
      name="notification"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="notification in appStore.notifications"
        :key="notification.id"
        class="max-w-sm"
      >
        <UiLiquidGlassCard class="overflow-hidden">
          <div class="flex items-start gap-3 p-4">
            <!-- Icon -->
            <div class="flex-shrink-0">
              <UIcon
                :name="getNotificationIcon(notification.type)"
                :class="getNotificationIconClass(notification.type)"
                class="w-5 h-5"
              />
            </div>

            <!-- Content -->
            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-medium text-white">
                {{ notification.title }}
              </h4>
              <p v-if="notification.message" class="text-sm text-white/70 mt-1">
                {{ notification.message }}
              </p>
            </div>

            <!-- Close Button -->
            <button
              class="flex-shrink-0 text-white/50 hover:text-white/80 transition-colors"
              @click="appStore.removeNotification(notification.id)"
            >
              <UIcon name="i-lucide-x" class="w-4 h-4" />
            </button>
          </div>
        </UiLiquidGlassCard>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()

function getNotificationIcon(type: string) {
  switch (type) {
    case 'success':
      return 'i-lucide-check-circle'
    case 'error':
      return 'i-lucide-x-circle'
    case 'warning':
      return 'i-lucide-alert-triangle'
    case 'info':
    default:
      return 'i-lucide-info'
  }
}

function getNotificationIconClass(type: string) {
  switch (type) {
    case 'success':
      return 'text-green-400'
    case 'error':
      return 'text-red-400'
    case 'warning':
      return 'text-yellow-400'
    case 'info':
    default:
      return 'text-blue-400'
  }
}
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
