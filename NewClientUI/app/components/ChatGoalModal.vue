<template>
  <UModal v-model="isOpen" :prevent-close="preventClose">
    <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
            Verhandlungsziel festlegen
          </h3>
          <UButton
            v-if="!preventClose"
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="handleClose"
          />
        </div>
      </template>

      <div class="space-y-4">
        <!-- Supplier Dropdown -->
        <div>
          <label for="supplier" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Lieferant
          </label>
          <USelectMenu
            v-model="selectedSupplier"
            :options="supplierOptions"
            placeholder="Wählen Sie e<PERSON> Lieferanten"
            value-attribute="value"
            option-attribute="label"
            @change="handleSupplierChange"
          >
            <template #option="{ option }">
              <span v-if="option.value === 'new'" class="font-semibold">
                {{ option.label }}
              </span>
              <span v-else-if="option.value === 'freies_gespraech'" class="italic">
                {{ option.label }}
              </span>
              <span v-else>
                {{ option.label }}
              </span>
            </template>
          </USelectMenu>
        </div>

        <!-- New Supplier Name Input (shown when "new" is selected) -->
        <div v-if="selectedSupplier === 'new'">
          <label for="newSupplierName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Name des neuen Lieferanten
          </label>
          <UInput
            v-model="newSupplierName"
            placeholder="Geben Sie den Namen des Lieferanten ein"
          />
        </div>

        <!-- Negotiation Goal Input (hidden for "freies_gespraech") -->
        <div v-if="selectedSupplier !== 'freies_gespraech'">
          <label for="goal" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Verhandlungsziel
          </label>
          <UTextarea
            v-model="negotiationGoal"
            placeholder="z.B. Jahresgespräch, Konditionenverhandlung etc."
            :rows="3"
          />
        </div>

        <!-- Info Text for Free Conversation -->
        <div v-if="selectedSupplier === 'freies_gespraech'" class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p class="text-sm text-blue-700 dark:text-blue-300">
            Sie haben "Freies Gespräch" gewählt. Es wird eine neue Sitzung ohne spezifisches Verhandlungsziel erstellt.
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <UButton
            v-if="!preventClose"
            color="gray"
            variant="ghost"
            @click="handleSkip"
          >
            Überspringen
          </UButton>
          <UButton
            color="primary"
            :disabled="!canSubmit"
            :loading="isCreating"
            @click="handleSubmit"
          >
            {{ selectedSupplier === 'freies_gespraech' ? 'Sitzung starten' : 'Weiter' }}
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import { useCollectionsStore } from '~/stores/collections'

interface Props {
  modelValue: boolean
  preventClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  preventClose: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'session-created': [sessionId: string]
}>()

const collectionsStore = useCollectionsStore()
const toast = useToast()

const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

const selectedSupplier = ref<string>('new')
const newSupplierName = ref('')
const negotiationGoal = ref('')
const isCreating = ref(false)
const existingFolders = ref<string[]>([])

// Build supplier options from existing folders
const supplierOptions = computed(() => {
  const options = [
    { value: 'new', label: 'Neuen Lieferanten erstellen' },
    { value: 'freies_gespraech', label: 'Freies Gespräch' }
  ]

  if (existingFolders.value.length > 0) {
    options.push({ value: 'separator', label: '---------------------------', disabled: true })
    existingFolders.value.forEach((folder) => {
      options.push({ value: folder, label: folder })
    })
  }

  return options
})

const canSubmit = computed(() => {
  if (selectedSupplier.value === 'freies_gespraech') {
    return true
  }

  if (selectedSupplier.value === 'new') {
    return newSupplierName.value.trim() !== '' && negotiationGoal.value.trim() !== ''
  }

  // Existing supplier selected
  return negotiationGoal.value.trim() !== ''
})

// Load existing folders when modal opens
watch(isOpen, async (value) => {
  if (value) {
    try {
      existingFolders.value = await collectionsStore.getSupplierFolders()
    } catch (error) {
      console.error('Error loading folders:', error)
    }
  }
})

function handleSupplierChange() {
  // Reset fields when supplier changes
  if (selectedSupplier.value !== 'new') {
    newSupplierName.value = ''
  }
}

async function handleSubmit() {
  if (!canSubmit.value) return

  isCreating.value = true

  try {
    let folderName = ''
    const goal = negotiationGoal.value.trim()

    if (selectedSupplier.value === 'freies_gespraech') {
      // Create session without folder and goal
      const session = await collectionsStore.createSession('', undefined)

      // Skip chat goals for this session
      if (session.id) {
        await collectionsStore.skipChatGoals(session.id)
      }

      emit('session-created', session.id)
      handleClose()
      return
    }

    // Determine folder name
    if (selectedSupplier.value === 'new') {
      folderName = newSupplierName.value.trim()

      // Create new folder if it doesn't exist
      if (!existingFolders.value.includes(folderName)) {
        await collectionsStore.createFolder(folderName)
      }
    } else {
      folderName = selectedSupplier.value
    }

    // Create session with goal and folder
    const session = await collectionsStore.createSession(goal, folderName)

    emit('session-created', session.id)
    handleClose()

    toast.add({
      title: 'Sitzung erstellt',
      description: `Neue Verhandlungssitzung für ${folderName} wurde erfolgreich erstellt.`,
      icon: 'i-heroicons-check-circle'
    })
  } catch (error) {
    console.error('Error creating session:', error)
    toast.add({
      title: 'Fehler',
      description: 'Die Sitzung konnte nicht erstellt werden. Bitte versuchen Sie es erneut.',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    isCreating.value = false
  }
}

async function handleSkip() {
  isCreating.value = true

  try {
    // Create session without goal
    const session = await collectionsStore.createSession('', undefined)

    // Skip chat goals
    if (session.id) {
      await collectionsStore.skipChatGoals(session.id)
    }

    emit('session-created', session.id)
    handleClose()
  } catch (error) {
    console.error('Error creating session:', error)
    toast.add({
      title: 'Fehler',
      description: 'Die Sitzung konnte nicht erstellt werden.',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    isCreating.value = false
  }
}

function handleClose() {
  // Reset form
  selectedSupplier.value = 'new'
  newSupplierName.value = ''
  negotiationGoal.value = ''
  isOpen.value = false
}
</script>
