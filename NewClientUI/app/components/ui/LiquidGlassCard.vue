<template>
  <div
    :class="[
      'liquid-glass-card',
      size === 'sm' ? 'p-2' : size === 'lg' ? 'p-8' : 'p-6',
      { 'liquid-glass-clear': clear }
    ]"
    :style="computedStyle"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'sm' | 'md' | 'lg'
  rounded?: boolean
  clear?: boolean
  opacity?: number // New prop: value between 0 and 1, default 0.01
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  rounded: false,
  clear: false,
  opacity: 0.01 // Default to the original opacity
})

const computedStyle = computed(() => {
  const whiteOpacity = Math.max(0, Math.min(1, props.opacity))
  const bgOpacity2 = 0.06 + (whiteOpacity * 0.1) // Scale the second gradient opacity

  return {
    borderRadius: props.rounded ? '16px' : '12px',
    background: `
      linear-gradient(0deg, rgba(255, 255, 255, ${whiteOpacity}), rgba(255, 255, 255, ${whiteOpacity})),
      linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, ${bgOpacity2}) 100%)
    `
  }
})
</script>

<style scoped>
.liquid-glass-card {
  /* Background is now set via computed style prop */

  backdrop-filter: blur(120px);
  -webkit-backdrop-filter: blur(120px);

  /* Border with gradient image source */
  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.04) 100%);
  border-image-slice: 1;

  box-shadow: 0px 5px 9px 0px rgba(255, 255, 255, 0.08) inset;

  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-glass-card > * {
  position: relative;
  z-index: 0;
}

.liquid-glass-card:hover {
  /* Enhanced hover state while maintaining Figma design */
  background:
    linear-gradient(0deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.02)),
    linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.08) 100%);

  border-image-source: linear-gradient(270deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.06) 100%);

  box-shadow:
    0px 5px 9px 0px rgba(255, 255, 255, 0.12) inset,
    0 0 26px rgba(135, 10, 43, 0.15),
    0 0 52px rgba(135, 10, 43, 0.1),
    0 0 78px rgba(135, 10, 43, 0.05);

  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
