<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="modelValue" class="dialog-overlay" @click.self="handleCancel">
        <Transition
          enter-active-class="transition ease-out duration-200"
          enter-from-class="opacity-0 transform scale-95"
          enter-to-class="opacity-100 transform scale-100"
          leave-active-class="transition ease-in duration-150"
          leave-from-class="opacity-100 transform scale-100"
          leave-to-class="opacity-0 transform scale-95"
        >
          <UiLiquidGlassCard v-if="modelValue" class="dialog-content" size="md" rounded :opacity="0.08">
            <!-- Header -->
            <div v-if="title" class="dialog-header">
              <h3 class="text-lg font-semibold text-white">
                {{ title }}
              </h3>
            </div>

            <!-- Body -->
            <div class="dialog-body">
              <!-- Message for simple dialogs -->
              <p v-if="message" class="text-white/80">
                {{ message }}
              </p>

              <!-- Input field for prompt dialogs -->
              <input
                v-if="type === 'prompt'"
                ref="inputRef"
                v-model="inputValue"
                type="text"
                class="dialog-input"
                :placeholder="placeholder"
                @keyup.enter="handleConfirm"
                @keyup.esc="handleCancel"
              >

              <!-- Custom slot content -->
              <slot />
            </div>

            <!-- Footer -->
            <div class="dialog-footer">
              <button
                v-if="showCancel"
                class="dialog-button dialog-button-cancel"
                @click="handleCancel"
              >
                {{ cancelText }}
              </button>
              <button
                class="dialog-button dialog-button-confirm"
                :disabled="type === 'prompt' && confirmDisabled"
                @click="handleConfirm"
              >
                {{ confirmText }}
              </button>
            </div>
          </UiLiquidGlassCard>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue'

interface Props {
  modelValue: boolean
  type?: 'alert' | 'confirm' | 'prompt'
  title?: string
  message?: string
  placeholder?: string
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  initialValue?: string
  required?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value?: string): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'alert',
  confirmText: 'OK',
  cancelText: 'Abbrechen',
  showCancel: true,
  placeholder: '',
  initialValue: '',
  required: false
})

const emit = defineEmits<Emits>()

const inputRef = ref<HTMLInputElement | null>(null)
const inputValue = ref(props.initialValue)

// Computed property to determine if confirm should be disabled
const confirmDisabled = computed(() => {
  return props.type === 'prompt' && props.required && !inputValue.value.trim()
})

// Focus input when dialog opens for prompt type
watch(() => props.modelValue, async (newVal) => {
  if (newVal && props.type === 'prompt') {
    inputValue.value = props.initialValue
    await nextTick()
    inputRef.value?.focus()
    inputRef.value?.select()
  }
})

const handleConfirm = () => {
  if (props.type === 'prompt') {
    if (props.required && !inputValue.value.trim()) {
      return
    }
    emit('confirm', inputValue.value)
  } else {
    emit('confirm')
  }
  emit('update:modelValue', false)
}

const handleCancel = () => {
  emit('cancel')
  emit('update:modelValue', false)
  if (props.type === 'prompt') {
    inputValue.value = props.initialValue
  }
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 9999;
  padding: 1rem;
}

.dialog-content {
  width: 100%;
  max-width: 28rem;
  max-height: 90vh;
  overflow-y: auto;
}

.dialog-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dialog-body {
  margin-bottom: 1.5rem;
}

.dialog-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: white;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  outline: none;
  transition: all 0.2s;
}

.dialog-input:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.05);
}

.dialog-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.dialog-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  outline: none;
}

.dialog-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dialog-button-confirm {
  color: white;
  background: linear-gradient(135deg, rgba(135, 10, 43, 0.4), rgba(135, 10, 43, 0.6));
  border-color: rgba(135, 10, 43, 0.3);
}

.dialog-button-confirm:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(135, 10, 43, 0.5), rgba(135, 10, 43, 0.7));
  box-shadow: 0 0 20px rgba(135, 10, 43, 0.3);
}

.dialog-button-cancel {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.dialog-button-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Custom scrollbar */
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
