<template>
  <div class="mesh-background" :class="{ 'mesh-animated': animated }">
    <div
      v-if="showMeshBox"
      class="mesh-box"
      :class="meshBoxPositionClass"
      :style="{ opacity: meshBoxOpacity }"
    />
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  animated?: boolean
  showMeshBox?: boolean
  meshBoxPosition?: 'left' | 'right' | 'center'
  meshBoxOpacity?: number
  variant?: 'default' | 'dark' | 'light'
}

const props = withDefaults(defineProps<Props>(), {
  animated: false,
  showMeshBox: true,
  meshBoxPosition: 'left',
  meshBoxOpacity: 0.4,
  variant: 'default'
})

const meshBoxPositionClass = computed(() => {
  switch (props.meshBoxPosition) {
    case 'right':
      return 'mesh-box-right'
    case 'center':
      return 'mesh-box-center'
    default:
      return 'mesh-box-left'
  }
})
</script>

<style scoped>
.mesh-background {
  background: linear-gradient(
    135deg,
    #050506 0%,
    #060505 25%,
    #0f141a 50%,
    #374151 75%,
    #4b5563 100%
  );
  position: relative;
  min-height: 100vh;
}

.mesh-animated {
  animation: meshDarken 8s ease-in-out infinite;
}

.mesh-box {
  position: absolute;
  top: 0%;
  height: 100%;
  z-index: 1;
  background-image:
    linear-gradient(rgba(120, 120, 120, 0.6) 1px, transparent 1px),
    linear-gradient(90deg, rgba(120, 120, 120, 0.6) 1px, transparent 1px);
  background-size: 70px 70px;
  pointer-events: none;
}

.mesh-box-left {
  left: 0%;
  width: 50%;
}

.mesh-box-right {
  right: 0%;
  width: 50%;
}

.mesh-box-center {
  left: 25%;
  width: 50%;
}

.mesh-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
      linear-gradient(to bottom right, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%),
      radial-gradient(circle at 6% 5%, rgba(25, 93, 113, 0.5) 0%, transparent 30%),
    radial-gradient(circle at 95% 90%, rgba(1, 13, 20, 0.9) 0%, transparent 60%),
    radial-gradient(circle at 24% 98%, rgba(135, 10, 43, 0.3) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.mesh-background > * {
  position: relative;
  z-index: 2;
}

@keyframes meshDarken {
  0%, 100% {
    background: linear-gradient(
      135deg,
      #050506 0%,
      #060505 25%,
      #0f141a 50%,
      #374151 75%,
      #4b5563 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      #040405 0%,
      #050404 25%,
      #0d1218 50%,
      #2e3749 75%,
      #3f4b59 100%
    );
  }
}

/* Variant styles */
.mesh-background.dark {
  background: linear-gradient(
    135deg,
    #000000 0%,
    #1a1a1a 25%,
    #2d2d2d 50%,
    #404040 75%,
    #525252 100%
  );
}

.mesh-background.dark.mesh-animated {
  animation: meshDarkenDark 8s ease-in-out infinite;
}

.mesh-background.light {
  background: linear-gradient(
    135deg,
    #f8fafc 0%,
    #e2e8f0 25%,
    #cbd5e1 50%,
    #94a3b8 75%,
    #64748b 100%
  );
}

.mesh-background.light.mesh-animated {
  animation: meshDarkenLight 8s ease-in-out infinite;
}

@keyframes meshDarkenDark {
  0%, 100% {
    background: linear-gradient(
      135deg,
      #000000 0%,
      #1a1a1a 25%,
      #2d2d2d 50%,
      #404040 75%,
      #525252 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      #000000 0%,
      #151515 25%,
      #252525 50%,
      #353535 75%,
      #454545 100%
    );
  }
}

@keyframes meshDarkenLight {
  0%, 100% {
    background: linear-gradient(
      135deg,
      #f8fafc 0%,
      #e2e8f0 25%,
      #cbd5e1 50%,
      #94a3b8 75%,
      #64748b 100%
    );
  }
  50% {
    background: linear-gradient(
      135deg,
      #e2e8f0 0%,
      #cbd5e1 25%,
      #b4bcc7 50%,
      #8394a4 75%,
      #5a6572 100%
    );
  }
}
</style>
