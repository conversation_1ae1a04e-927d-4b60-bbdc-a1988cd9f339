<template>
  <UiLiquidDialog
    v-model="dialogStore.isOpen"
    :type="dialogStore.type"
    :title="dialogStore.title"
    :message="dialogStore.message"
    :confirm-text="dialogStore.confirmText"
    :cancel-text="dialogStore.cancelText"
    :placeholder="dialogStore.placeholder"
    :initial-value="dialogStore.initialValue"
    :required="dialogStore.required"
    :show-cancel="dialogStore.showCancel"
    @confirm="dialogStore.handleConfirm"
    @cancel="dialogStore.handleCancel"
  />
</template>

<script setup lang="ts">
import { useDialogStore } from '~/stores/dialog'

const dialogStore = useDialogStore()
</script>
