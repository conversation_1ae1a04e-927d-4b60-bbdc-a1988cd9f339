<template>
  <div class="auth-loader">
    <div class="mesh-box" />
    <div class="loader-content">
      <img src="/images/fbeta.png" alt="fbeta" class="logo">
      <div class="loader-spinner">
        <UIcon name="i-lucide-loader-2" class="spinner-icon" />
      </div>
      <p class="loader-text">
        {{ displayText }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  loadingText: ''
})

const { getToken } = useAuth()

const displayText = computed(() => {
  if (props.loadingText) return props.loadingText

  // Check if we're handling OAuth callback
  const urlParams = new URLSearchParams(window.location.search)
  if (urlParams.has('access_token')) {
    return 'Setting up your session...'
  }

  // Check if we have a token (returning user)
  if (getToken()) {
    return 'Loading your workspace...'
  }

  // Default for new auth
  return 'Redirecting to login...'
})
</script>

<style scoped>
.auth-loader {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  z-index: 9999;
}

.mesh-box {
  position: absolute;
  inset: 0;
  opacity: 0.3;
}

.loader-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.logo {
  height: 2.5rem;
  animation: fadeIn 0.5s ease-in-out;
}

.loader-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: rgba(255, 255, 255, 0.6);
  animation: spin 1s linear infinite;
}

.loader-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
