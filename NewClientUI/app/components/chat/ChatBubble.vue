<template>
  <div class="chat-bubble-container" :class="{ 'user-message': isUser, 'assistant-message': !isUser }">
    <UiLiquidGlassCard
      v-if="isUser"
      class="chat-bubble"
      :class="{
        'user-bubble': isUser
      }"
      size="sm"
    >
      <div class="message-content">
        <p class="text-white whitespace-pre-wrap message-text">
          {{ message }}
        </p>
      </div>
    </UiLiquidGlassCard>

    <div
      v-else
      class="chat-bubble assistant-bubble"
    >
      <div class="message-content">
        <div class="message-header">
          <img src="/images/sparkle.svg" alt="sparkle" class="sparkle-icon">
          <div class="message-text">
            <div v-if="message" v-html="formattedMessage" />
            <div v-else-if="isStreaming" class="streaming-indicator">
              <span class="dot" />
              <span class="dot" />
              <span class="dot" />
            </div>
          </div>
        </div>
      </div>

      <div v-if="!isStreaming" class="message-actions">
        <UButton
          :icon="isSpeaking ? 'i-lucide-stop-circle' : 'i-lucide-volume-2'"
          variant="ghost"
          size="md"
          class="text-white mx-1"
          :style="isSpeaking ? speakingStyle : {}"
          @click="toggleSpeak"
        />
        <UTooltip
          :text="copied ? 'Copied!' : 'Copy'"
          :content="{
            align: 'center',
            side: 'top',
            sideOffset: 8
          }"
        >
          <UButton
            :icon="copied ? 'i-lucide-check' : 'i-lucide-copy'"
            variant="ghost"
            size="md"
            :class="copied ? 'text-green-400' : 'text-white'"
            @click="copyMessage"
          />
        </UTooltip>
        <div class="relative inline-block">
          <div ref="actionsTriggerEl">
            <UButton
              icon="i-lucide-sliders-vertical"
              variant="ghost"
              size="md"
              class="text-white mt-2 mx-1"
              @click="toggleActionsPopover"
            />
          </div>
        </div>
        <UButton
          :icon="isFavorited ? 'i-lucide-star' : 'i-lucide-star'"
          :class="['text-white', isFavorited ? 'text-yellow-400' : '']"
          variant="ghost"
          size="md"
          @click="toggleFavorite"
        />
        <UButton
          icon="i-lucide-search"
          variant="ghost"
          size="md"
          class="text-white cursor-pointer"
          @click="emit('show-sources', { id: messageId, sources: retrievedDocs })"
        >
          {{ sourceCount }} sources
        </UButton>

        <Teleport to="body">
          <Transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div v-if="showActions" class="actions-overlay" @click="closeActions">
              <div class="actions-popover-wrapper" :style="actionsStyle" @click.stop>
                <UiLiquidGlassCard class="actions-popover" size="xs" rounded>
                  <button type="button" class="action-item" @click="handleRefineAction('shorter_answer')">
                    <UIcon name="i-lucide-arrow-down-wide-narrow" class="w-4 h-4" />
                    <span>Kürzere Antwort</span>
                  </button>
                  <button type="button" class="action-item" @click="handleRefineAction('longer_answer')">
                    <UIcon name="i-lucide-expand" class="w-4 h-4" />
                    <span>Längere Antwort</span>
                  </button>
                  <button type="button" class="action-item" @click="handleRefineAction('less_detail')">
                    <UIcon name="i-lucide-minus-circle" class="w-4 h-4" />
                    <span>Weniger Details</span>
                  </button>
                  <button type="button" class="action-item" @click="handleRefineAction('more_detail')">
                    <UIcon name="i-lucide-plus-circle" class="w-4 h-4" />
                    <span>Mehr Details</span>
                  </button>
                </UiLiquidGlassCard>
              </div>
            </div>
          </Transition>
        </Teleport>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { marked } from 'marked'

interface Props {
  message: string
  isUser: boolean
  isStreaming?: boolean
  sourceCount?: number
  messageId?: string
  retrievedDocs?: unknown[]
  isFavorite?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'copy' | 'speak' | 'stop-speak', id?: string): void
  (e: 'refine', action: string, id?: string): void
  (e: 'toggle-favorite', id?: string, next: boolean): void
  (e: 'show-sources', payload: { id?: string, sources?: unknown[] }): void
}>()

const isSpeaking = ref(false)
const isFavorited = ref(props.isFavorite)
const messageId = computed(() => props.messageId)

watch(() => props.isFavorite, (newVal) => {
  isFavorited.value = !!newVal
})

const copied = ref(false)
let copiedTimer: number | undefined

const showActions = ref(false)
const actionsStyle = ref<Record<string, string | number>>({})
const actionsTriggerEl = ref<HTMLElement | null>(null)

const speakingStyle = computed(() => ({
  animation: 'speakPulse 1.5s ease-in-out infinite'
}))

const formattedMessage = computed(() => {
  if (props.isUser || !props.message) {
    return props.message
  }

  try {
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: false,
      mangle: false
    })

    return marked(props.message)
  } catch (error) {
    console.error('Error parsing markdown:', error)
    return props.message
  }
})

const copyMessage = () => {
  if (navigator.clipboard && props.message) {
    navigator.clipboard.writeText(props.message)
      .then(() => {
        emit('copy', messageId.value)
        copied.value = true
        if (copiedTimer) window.clearTimeout(copiedTimer)
        copiedTimer = window.setTimeout(() => {
          copied.value = false
        }, 2000) as unknown as number
      })
      .catch((err) => {
        console.error('Failed to copy:', err)
      })
  }
}

const toggleActionsPopover = () => {
  if (showActions.value) {
    closeActions()
  } else {
    openActions()
  }
}

const openActions = () => {
  showActions.value = true
  nextTick(() => updateActionsPosition())
}

const closeActions = () => {
  showActions.value = false
}

const updateActionsPosition = () => {
  const trigger = actionsTriggerEl.value
  if (!trigger) {
    return
  }

  const el = trigger

  if (!el || typeof el.getBoundingClientRect !== 'function') {
    return
  }

  const rect = el.getBoundingClientRect()
  const dropdownWidth = 180
  const dropdownHeight = 180
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const spacing = 8

  // Start with bottom-right positioning
  let left = rect.right + spacing
  let top = rect.top

  if (left + dropdownWidth > viewportWidth - 10) {
    left = rect.left - dropdownWidth - spacing
  }

  if (top + dropdownHeight > viewportHeight - 10) {
    top = rect.top - dropdownHeight + rect.height
  }

  if (top < 10) {
    top = 10
  }

  if (left < 10) {
    left = 10
  }

  actionsStyle.value = {
    position: 'fixed',
    left: `${left}px`,
    top: `${top}px`,
    zIndex: 9999
  }
}

const toggleFavorite = () => {
  isFavorited.value = !isFavorited.value
  emit('toggle-favorite', messageId.value, isFavorited.value)
}

const toggleSpeak = () => {
  if (!props.message) return
  if ('speechSynthesis' in window) {
    if (isSpeaking.value) {
      window.speechSynthesis.cancel()
      isSpeaking.value = false
      emit('stop-speak', messageId.value)
    } else {
      // Extract plain text from the parsed HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = formattedMessage.value
      const plainText = tempDiv.textContent || tempDiv.innerText || ''

      const utter = new SpeechSynthesisUtterance(plainText)
      utter.lang = 'de-DE'
      utter.rate = 0.75
      utter.onend = () => {
        isSpeaking.value = false
        emit('stop-speak', messageId.value)
      }
      isSpeaking.value = true
      emit('speak', messageId.value)
      window.speechSynthesis.speak(utter)
    }
  }
}

const handleRefineAction = (action: string) => {
  emit('refine', action, messageId.value)
  closeActions()
}

onUnmounted(() => {
  if ('speechSynthesis' in window) {
    window.speechSynthesis.cancel()
  }
  if (copiedTimer) window.clearTimeout(copiedTimer)
})
</script>

<style scoped>
.chat-bubble-container {
  display: flex;
  width: 100%;
  margin-bottom: 1rem;
}

.user-message {
  justify-content: flex-end;
}

.assistant-message {
  justify-content: flex-start;
}

.chat-bubble {
  max-width: 70%;
  min-width: 200px;
}

.assistant-bubble {
  padding: 1rem;
}

.message-content {
  margin-bottom: 0.75rem;
}

.message-header {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.sparkle-icon {
  color: #AD3A4F;
  font-size: 1rem;
  line-height: 1.5;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.message-text {
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  line-height: 24px;
  color: white;
  white-space: pre-wrap;
  margin-left: 20px;
  flex: 1;
  font-family: 'Lato';
}

.message-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 10%;
  padding-top: 0.5rem;
  flex-wrap: wrap;
}

@keyframes speakPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.8;
  }
}

.streaming-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.streaming-indicator .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  animation: pulse 1.4s infinite ease-in-out;
}

.streaming-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.streaming-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.streaming-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.message-text :deep(p) {
  margin-bottom: 0.5rem;
}

.message-text :deep(pre) {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  overflow-x: auto;
}

.message-text :deep(code) {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.125rem 0.25rem;
  border-radius: 2px;
  font-size: 0.875em;
}

.message-text :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

.message-text :deep(ul),
.message-text :deep(ol) {
  margin-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.message-text :deep(li) {
  margin-bottom: 0.25rem;
}

.actions-overlay {
  position: fixed;
  inset: 0;
  z-index: 9999;
}

.actions-popover-wrapper {
  position: fixed;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
}

.actions-popover {
  padding: 0.5rem;
  min-width: 180px;
  left: 120px;
  bottom: 20px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
  border-radius: 0.375rem;
  transition: all 0.15s;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.action-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.star-favorited :deep(svg) {
  fill: #facc15 !important;
  stroke: #facc15 !important;
}

.star-unfavorited :deep(svg) {
  fill: none;
  stroke: currentColor;
}

@media (max-width: 768px) {
  .chat-bubble {
    max-width: 85%;
  }

  .message-actions {
    gap: 0.25rem;
  }
}
</style>
