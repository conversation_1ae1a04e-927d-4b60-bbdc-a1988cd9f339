<template>
  <div class="chat-input-container">
    <UiLiquidGlassCard class="chat-input-card" size="sm">
      <form class="chat-form" @submit.prevent="handleSubmit">
        <div class="input-row">
          <!-- Collection Selector -->
          <CollectionSelector
            v-model="selectedCollectionId"
            @change="selectCollection"
          />

          <UButton
            type="submit"
            icon="i-lucide-paperclip"
            variant="ghost"
            class="send-btn text-secondary-custom"
            :loading="loading"
          />

          <!-- Input field -->
          <input
            v-model="inputValue"
            type="text"
            placeholder="Ask a question..."
            class="chat-input"
            :disabled="loading"
          >

          <!-- Send button -->
          <UButton
            type="submit"
            icon="i-lucide-mic"
            variant="ghost"
            class="send-btn text-secondary-custom mx-2"
          />
        </div>
      </form>
    </UiLiquidGlassCard>
  </div>
</template>

<script setup lang="ts">
import { useCollectionsStore } from '~/stores/collections'
import { useRouter } from 'vue-router'
import CollectionSelector from './CollectionSelector.vue'

interface Props {
  loading?: boolean
}

interface Emits {
  (e: 'submit', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()
const collectionsStore = useCollectionsStore()
const router = useRouter()

const inputValue = ref('')

const selectedCollectionId = computed({
  get: () => collectionsStore.currentCollection?.id || '',
  set: (value: string) => {
    const collection = collectionsStore.collections.find(c => c.id === value)
    if (collection) {
      selectCollection(collection)
    }
  }
})

const selectCollection = async (collection: typeof collectionsStore.collections[0]) => {
  collectionsStore.selectCollection(collection)
  sessionStorage.setItem('selectedCollection', JSON.stringify(collection))
  router.go(0)
}

const handleSubmit = () => {
  if (!inputValue.value.trim() || props.loading) return

  emit('submit', inputValue.value.trim())
  inputValue.value = ''
}

onMounted(async () => {
  if (!collectionsStore.hasCollections) {
    await collectionsStore.fetchCollections()
  }
})
</script>

<style scoped>
.chat-input-container {
  width: 100%;
  z-index: 10;
}
.chat-input-card {
  width: 100%;
  overflow: visible !important;
}

.chat-form {
  width: 100%;
  overflow: visible !important;
}

/* Ensure the glass card doesn't clip the dropdown */
:deep(.ui-liquid-glass-card) {
  overflow: visible !important;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.chat-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: white;
  font-size: 0.875rem;
  placeholder-color: rgba(255, 255, 255, 0.5);
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.chat-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-btn {
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.25) !important;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .input-row {
    gap: 0.5rem;
  }
}

/* Ensure input works on mobile */
@media (max-width: 480px) {
  .collection-select {
    display: none;
  }
}
</style>
