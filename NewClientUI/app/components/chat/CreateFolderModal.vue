<template>
  <UModal v-model="isOpen" :ui="{ wrapper: 'z-[100]' }">
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold text-white">
          Neuen Ordner erstellen
        </h3>
      </template>

      <div class="space-y-4">
        <UFormGroup label="Ordnername" required>
          <UInput
            v-model="folderName"
            placeholder="Ordnernamen eingeben"
            class="w-full"
            :ui="{
              base: 'bg-white/10 border-white/20 text-white',
              placeholder: 'placeholder-white/50',
              input: 'text-white'
            }"
            @keyup.enter="handleCreate"
          />
        </UFormGroup>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <UButton
            label="Abbrechen"
            variant="ghost"
            color="gray"
            @click="handleCancel"
          />
          <UButton
            label="Erstellen"
            :disabled="!folderName.trim()"
            @click="handleCreate"
          />
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useCollectionsStore } from '~/stores/collections'
import { useToast } from '#imports'

const collectionsStore = useCollectionsStore()
const toast = useToast()

const isOpen = ref(false)
const folderName = ref('')

const handleCreate = async () => {
  const trimmedName = folderName.value.trim()

  if (!trimmedName) {
    toast.add({
      title: 'Fehler',
      description: 'Bitte geben Sie einen Ordnernamen ein.',
      color: 'red'
    })
    return
  }

  try {
    await collectionsStore.createFolder(trimmedName)

    toast.add({
      title: 'Erfolg',
      description: `Ordner "${trimmedName}" wurde erfolgreich erstellt.`,
      color: 'green'
    })

    // Reset and close
    folderName.value = ''
    isOpen.value = false
  } catch (error: any) {
    console.error('Error creating folder:', error)

    // Check for specific error messages
    let errorMessage = 'Ordner konnte nicht erstellt werden.'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.message) {
      errorMessage = error.message
    }

    toast.add({
      title: 'Fehler',
      description: errorMessage,
      color: 'red'
    })
  }
}

const handleCancel = () => {
  folderName.value = ''
  isOpen.value = false
}

// Listen for the event to show the modal
const showModal = () => {
  folderName.value = ''
  isOpen.value = true
}

onMounted(() => {
  window.addEventListener('show-create-folder-modal', showModal)
})

onUnmounted(() => {
  window.removeEventListener('show-create-folder-modal', showModal)
})
</script>
