@import "tailwindcss" theme(static);

@theme static {
  --font-sans: 'Roboto', sans-serif;

}

:root {
  --ui-container: var(--container-3xl);

  --color-primary: #ffffff;
  --color-primary-foreground: #0f172a;

  --color-hr: #ef4444; /* Red/Pink */
  --color-legal: #3b82f6; /* Blue */
  --color-compliance: #10b981; /* Green/Teal */
  --color-esg: #9333ea; /* Purple */

  /* Dark theme backgrounds */
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-surface-elevated: #334155;

  /* Text colors */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #ad3a4f;
  --color-text-muted: #94a3b8;

  /*Button colors */
  --color-button-primary: #2c6a80;
  --color-button-primary-foreground: #ffffff;
  --color-button-secondary: #f8fafc;
  --color-button-secondary-foreground: #0f172a;

  /* Glass effect */
  --glass-bg: rgba(51, 65, 85, 0.8);
  --glass-border: rgba(148, 163, 184, 0.2);
}

/* Utility classes for theme colors */
.text-primary-custom { color: var(--color-text-primary); }
.text-secondary-custom { color: var(--color-text-secondary); }
.text-muted-custom { color: var(--color-text-muted); }

.bg-surface { background-color: var(--color-surface); }
.bg-surface-elevated { background-color: var(--color-surface-elevated); }
.bg-background { background-color: var(--color-background); }

/* Accent color utilities */
.text-hr { color: var(--color-hr); }
.text-legal { color: var(--color-legal); }
.text-compliance { color: var(--color-compliance); }
.text-esg { color: var(--color-esg); }

/* Background color utilities */
.btn-primary-custom {
  background-color: var(--color-button-primary);
  color: var(--color-button-primary-foreground);
}
.btn-secondary-custom {
  background-color: var(--color-button-secondary);
  color: var(--color-button-secondary-foreground);
}

.bg-hr { background-color: var(--color-hr); }
.bg-legal { background-color: var(--color-legal); }
.bg-compliance { background-color: var(--color-compliance); }
.bg-esg { background-color: var(--color-esg); }

/* Glass effect utility */
.glass {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Button size fixes after removing Nuxt UI Pro */
.btn-lg {
  padding: 0.875rem 2rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  border-radius: 0.5rem;
}

/* Icon size fixes */
/*.icon-sm { */
/*  width: 1.2rem;*/
/*  height: 1.2rem;*/
/*}*/

/*.icon-md { */
/*  width: 1.5rem; */
/*  height: 1.5rem; */
/*}*/

/*.icon-lg { */
/*  width: 2rem; */
/*  height: 2rem; */
/*}*/

/*.icon-xl { */
/*  width: 2.5rem; */
/*  height: 2.5rem; */
/*}*/

/* UButton size overrides to match previous UI Pro sizes */
.btn[data-size="sm"] {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn[data-size="lg"] {
  padding: 0.875rem 2rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  min-height: 3rem;
}

/* Fix for icon sizes in buttons */
button .iconify,
.btn .iconify {
  width: 1rem;
  height: 1rem;
}

button[data-size="sm"] .iconify,
.btn[data-size="sm"] .iconify {
  width: 1rem;
  height: 1rem;
}

button[data-size="lg"] .iconify,
.btn[data-size="lg"] .iconify {
  width: 1.5rem;
  height: 1.5rem;
}

/* UIcon specific fixes */
.u-icon {
  display: inline-block;
  flex-shrink: 0;
}

/* Layout stability fixes */
.min-h-screen {
  min-height: 100vh;
  min-height: 100dvh; /* Use dynamic viewport height when supported */
}

/* Prevent layout shift during loading states */
.loading-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Button layout stability */
button, .btn {
  transition: all 0.15s ease-in-out;
}

/* Fixed card dimensions to prevent layout shift */
.liquid-glass-card {
  transition: transform 0.2s ease-in-out;
}

/* Ensure consistent icon rendering */
.iconify, .u-icon {
  vertical-align: middle;
}

.mesh-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  z-index: 0;
  opacity: 0.4;
  background-image:
          linear-gradient(rgba(95, 91, 91, 0.3) 1px, transparent 1px),
          linear-gradient(90deg, rgba(95, 91, 91, 0.3) 1px, transparent 1px);
  background-size: 70px 70px;
}
