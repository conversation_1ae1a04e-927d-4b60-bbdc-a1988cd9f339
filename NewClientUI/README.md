# Fbeta Chat Application

This is a chat application built with Nuxt.js, designed for a streamlined development experience.

## Features

*   **Vue 3 & Nuxt 3:** Modern frontend stack.
*   **TypeScript:** Type-safe development.
*   **Nuxt UI Pro:** Beautiful and functional UI components.
*   **Mock API:** Simplified local development with mock data.

## Setup

### Prerequisites

*   Node.js (v18 or higher recommended)
*   pnpm (recommended package manager)

### Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd fbeta/chat # Or wherever you cloned it
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```
### Development

To start the development server:

```bash
npm run dev
```

The application will be accessible at `http://localhost:3000`.
